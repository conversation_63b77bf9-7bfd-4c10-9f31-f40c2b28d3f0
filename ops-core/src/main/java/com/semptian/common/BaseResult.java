package com.semptian.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/11/5 0005 16:06
 */
@Data
@AllArgsConstructor
@RequiredArgsConstructor
public class BaseResult implements Serializable {

    private static final long SERIAL_VERSION_ID = 1L;

    /** 返回状态码 */
    @NonNull private Integer code;

    /** 返回结果信息 */
    @NonNull private String message;

    /** 返回结果数据 */
    private Object data;


}
