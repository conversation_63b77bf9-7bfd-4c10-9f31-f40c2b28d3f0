package com.semptian.model.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/9/22
 * @Description
 **/
@ApiModel(value = "用户修改对象")
@Data
public class UserUpdateModel {

    @ApiModelProperty(value = "用户id")
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "用户账号")
    private String account;

    @ApiModelProperty(value = "描述", example = "faf")
    private String userDesc;

    @ApiModelProperty(value = "用户手机号")
    private String telephone;

    @ApiModelProperty(value = "用户性别: 1 - 男 ; 0 - 女")
    private int gender;

    @ApiModelProperty(value = "过期时间:格式yyyy-MM-dd;永久有效 - permanent;一个月 - one_month;两个月 - two_month")
    private String expiration;

    @ApiModelProperty(value = "用户为审批角色可管理的组织,多个id直接用逗号分隔")
    private String manageOrgs;

    @ApiModelProperty(value = "用户默认图像地址")
    private String defaultImageUrl;
    @ApiModelProperty(value = "用户指纹信息")
    private String finger;

}
