package com.semptian.component;

import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.nio.file.Path;
import java.time.LocalDate;

/**
 * 文件系统工具测试类
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class FileSystemUtilTest {

    @InjectMocks
    private FileSystemUtil fileSystemUtil;

    @Before
    public void setUp() {
        // 设置测试配置
        ReflectionTestUtils.setField(fileSystemUtil, "basePath", "/tmp/test_export");
        ReflectionTestUtils.setField(fileSystemUtil, "diskThreshold", 70);
    }

    @Test
    public void testCheckDiskUsage() {
        // 测试磁盘使用率检查
        boolean result = fileSystemUtil.checkDiskUsage();
        log.info("磁盘使用率检查结果: {}", result);
        // 通常情况下应该返回true（除非磁盘真的满了）
        assert result;
    }

    @Test
    public void testCreateExportFilePath() {
        // 测试文件路径创建
        Integer metricId = 1;
        String protocolType = "email";
        long timestamp = System.currentTimeMillis();
        LocalDate targetDate = LocalDate.of(2024, 5, 9);

        Path filePath = fileSystemUtil.createExportFilePath(metricId, protocolType, timestamp, targetDate);
        log.info("生成的文件路径: {}", filePath.toString());

        // 验证路径格式
        String pathStr = filePath.toString();
        assert pathStr.contains(metricId.toString());
        assert pathStr.contains(protocolType);
        assert pathStr.contains("搜索");
        assert pathStr.contains("ads");
        assert pathStr.contains("2024-05-09");
        assert pathStr.endsWith(".csv");
    }

    @Test
    public void testCleanupFiles() {
        // 测试文件清理（不会实际删除文件，只是验证方法不抛异常）
        try {
            LocalDate targetDate = LocalDate.of(2024, 5, 9);
            fileSystemUtil.cleanupFiles(1, targetDate);
            log.info("文件清理测试通过");
        } catch (Exception e) {
            log.error("文件清理测试失败", e);
            throw e;
        }
    }
}
