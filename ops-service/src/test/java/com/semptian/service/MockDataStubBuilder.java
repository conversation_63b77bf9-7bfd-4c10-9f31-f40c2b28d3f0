package com.semptian.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.semptian.aspect.StaticMethodRecorderProxy;
import com.semptian.service.impl.BusinessApiConfigServiceImpl;
import lombok.Getter;
import lombok.Setter;
import lombok.var;
import org.mockito.Mockito;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.io.IOException;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Mock自动打桩构造器
 *
 * <AUTHOR>
 * &#064;date  2025/4/9 下午12:27
 */
public class MockDataStubBuilder {
    private static final ObjectMapper mapper = new ObjectMapper();

    static {
        // 配置 ObjectMapper 忽略未知属性
        mapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 注册 HttpResponse 的自定义反序列化器
        SimpleModule module = new SimpleModule();
        module.addDeserializer(HttpResponse.class, new HttpResponseDeserializer());

        // 注册ValueOperations的自定义反序列化器
        module.addDeserializer(ValueOperations.class, new ValueOperationsDeserializer());
        module.addDeserializer(RedisTemplate.class, new RedisTemplateDeserializer());
        mapper.registerModule(module);
    }

    /**
     * HttpResponse 的自定义反序列化器
     */
    static class HttpResponseDeserializer extends JsonDeserializer<HttpResponse> {
        @Override
        public HttpResponse deserialize(JsonParser p, DeserializationContext ctxt) {
            // 返回一个模拟的 HttpResponse 对象
            // 这里我们使用 Mockito 创建一个模拟对象
            return Mockito.mock(HttpResponse.class);
        }
    }

    /**
     * ValueOperations 的自定义反序列化器
     */
    static class ValueOperationsDeserializer extends JsonDeserializer<ValueOperations> {
        @Override
        public ValueOperations deserialize(JsonParser p, DeserializationContext ctxt) {
            // 返回一个模拟的 ValueOperations 对象
            // 这里我们使用 Mockito 创建一个模拟对象
            return Mockito.mock(ValueOperations.class);
        }
    }

    /**
     * RedisTemplate 的自定义反序列化器
     */
    static class RedisTemplateDeserializer extends JsonDeserializer<RedisTemplate> {
        @Override
        public RedisTemplate deserialize(JsonParser p, DeserializationContext ctxt) {
            // 返回一个模拟的 RedisTemplate 对象
            // 这里我们使用 Mockito 创建一个模拟对象
            return Mockito.mock(RedisTemplate.class);
        }
    }

    /**
     * 构建Mock对象并自动打桩
     *
     * @param serviceClass 要Mock的服务类
     * @return 已配置桩的Mock对象
     */
    public static <T> T buildMock(Class<T> serviceClass) {
        //通过类的class对象，获取Mock出来的、该类的实例对象
        T mock = Mockito.mock(serviceClass);
        //传入class对象和mock出的实例对象，应用桩配置
        applyStubbing(mock, serviceClass);
        return mock;
    }

    /**
     * 应用桩配置
     */
    private static <T> void applyStubbing(T mock, Class<T> serviceClass) {
        //根据类名加载Mock记录，MockRecord包含类名,方法名,传入的参数,返回值,时间戳
        List<MockRecord> records = loadMockRecords(serviceClass);

        if (null == records || records.isEmpty()) {
            System.out.println("没有找到Mock记录" + serviceClass.getSimpleName());
            return;
        }

        records.forEach(record -> {
            try {
                // 通过反射找到匹配方法
                record.findMatchingMethod(serviceClass).ifPresent(method -> {
                    try {
                        // 配置Mock行为
                        Object[] args = record.parseArguments(method.getParameterTypes());
                        Object returnValue = record.parseReturnValue(method);
                        Mockito.when(method.invoke(mock, args)).thenReturn(returnValue);
                    } catch (Exception e) {
                        throw new MockSetupException("桩配置失败: " + record.method, e);
                    }
                }
                );

            } catch (Exception e) {
                System.err.println("跳过无效记录: " + record.method + "参数为：" + record.args);
            }
        });
    }

    /**
     * 加载Mock记录,文件名格式：类名_方法名_参数md5.json,一个文件是一个方法的记录
     */
    /**
     * 加载Mock记录,适配新的目录结构：类名目录/方法名目录/参数md5.json
     */
    private static List<MockRecord> loadMockRecords(Class<?> serviceClass) {
        try {
            // 获取类名目录，首先尝试完整类名
            String className = serviceClass.getSimpleName();
            Path classDir = StaticMethodRecorderProxy.TEST_DIR.resolve(className);

            // 如果类目录不存在，尝试查找接口名称（去掉Impl后缀）
            if (!Files.exists(classDir) && className.endsWith("Impl")) {
                String interfaceName = className.substring(0, className.length() - 4); // 去掉"Impl"后缀
                Path interfaceDir = StaticMethodRecorderProxy.TEST_DIR.resolve(interfaceName);

                if (Files.exists(interfaceDir)) {
                    System.out.println("找到接口目录: " + interfaceDir);
                    classDir = interfaceDir;
                } else {
                    System.out.println("未找到类目录: " + classDir + " 或接口目录: " + interfaceDir + "，尝试查找旧格式文件...");
                    return loadLegacyMockRecords(serviceClass);
                }
            } else if (!Files.exists(classDir)) {
                // 如果不是Impl结尾，尝试查找带Impl后缀的目录
                String implClassName = className + "Impl";
                Path implClassDir = StaticMethodRecorderProxy.TEST_DIR.resolve(implClassName);

                if (Files.exists(implClassDir)) {
                    System.out.println("找到实现类目录: " + implClassDir);
                    classDir = implClassDir;
                } else {
                    System.out.println("未找到类目录: " + classDir + " 或实现类目录: " + implClassDir + "，尝试查找旧格式文件...");
                    return loadLegacyMockRecords(serviceClass);
                }
            }

            List<MockRecord> records = new ArrayList<>();

            // 遍历类目录下的所有方法目录
            try (var classDirStream = Files.list(classDir)) {
                classDirStream.filter(Files::isDirectory).forEach(methodDir -> {
                    try (var methodDirStream = Files.list(methodDir)) {
                        // 遍历方法目录下的所有JSON文件
                        methodDirStream.filter(path -> path.toString().endsWith(".json")).forEach(jsonFile -> {
                            try {
                                // 读取并解析JSON文件
                                MockRecord record = mapper.readValue(jsonFile.toFile(), MockRecord.class);
                                records.add(record);
                            } catch (IOException e) {
                                System.err.println("解析Mock记录失败: " + jsonFile.getFileName() + ", 错误: " + e.getMessage());
                            }
                        });
                    } catch (IOException e) {
                        System.err.println("读取方法目录失败: " + methodDir.getFileName() + ", 错误: " + e.getMessage());
                    }
                });
            }

            if (records.isEmpty()) {
                System.out.println("在新目录结构中未找到Mock记录，尝试查找旧格式文件...");
                List<MockRecord> legacyRecords = loadLegacyMockRecords(serviceClass);
                if (legacyRecords != null && !legacyRecords.isEmpty()) {
                    return legacyRecords;
                }
            }

            return records;
        } catch (IOException e) {
            System.err.println("加载Mock记录失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 加载旧格式的Mock记录（兼容旧版本）
     * 旧格式：类名_方法名_参数md5.json
     */
    private static List<MockRecord> loadLegacyMockRecords(Class<?> serviceClass) {
        try {
            return Files.walk(StaticMethodRecorderProxy.TEST_DIR)
                    .filter(Files::isRegularFile)
                    .map(Path::toFile)
                    .filter(file -> file.getName().startsWith(serviceClass.getSimpleName() + "_"))
                    .map(file -> {
                        try {
                            return mapper.readValue(file, MockRecord.class);
                        } catch (IOException e) {
                            throw new MockSetupException("解析Mock记录失败: " + file.getName(), e);
                        }
                    }).collect(Collectors.toList());
        } catch (IOException e) {
            System.err.println("加载旧格式Mock记录失败: " + e.getMessage());
            return null;
        }
    }


    /**
     * Mock记录数据结构
     */
    @Setter
    @Getter
    static class MockRecord {
        // Getter 和 Setter 方法
        private String className;
        private String method; // 改为 method 以匹配 JSON 文件
        private Object args; // 改为 args 以匹配 JSON 文件
        private Object result;
        private String timestamp; // 添加 timestamp 字段
        private String[] argsType; // 添加 argsType 字段，用于存储参数的实际类型

        // 无参构造函数，Jackson 需要
        public MockRecord() {
        }

        /**
         * 查找匹配的方法
         */
        Optional<java.lang.reflect.Method> findMatchingMethod(Class<?> targetClass) {
            // 创建一个方法列表，用于存储所有方法
            Set<java.lang.reflect.Method> allMethods = new HashSet<>();

            // 获取当前类的所有方法（包括私有方法）
            allMethods.addAll(Arrays.asList(targetClass.getDeclaredMethods()));

            // 获取该类的所有公共方法（包括继承的方法）
            allMethods.addAll(Arrays.asList(targetClass.getMethods()));

            // 获取该类实现的所有接口的方法
            for (Class<?> interfaceClass : targetClass.getInterfaces()) {
                allMethods.addAll(Arrays.asList(interfaceClass.getMethods()));
            }

            // 从所有方法中查找匹配的方法
            return allMethods.stream()
                    // 过滤出方法名匹配的方法
                    .filter(m -> m.getName().equals(method))
                    // 过滤出参数数量匹配的方法，方法名+方法参数可以确定唯一的方法
                    .filter(m -> {
                        // 检查参数数量是否匹配，参数数量匹配就认为是
                        if (args instanceof Object[]) {
                            return m.getParameterCount() == ((Object[]) args).length;
                        } else if (args instanceof List) {
                            return m.getParameterCount() == ((List<?>) args).size();
                        }
                        return m.getParameterCount() == 0;
                    })
                    // 尝试先匹配非接口方法，因为实现类的方法更可能是我们想要的
                    .sorted((m1, m2) -> {
                        boolean m1FromInterface = m1.getDeclaringClass().isInterface();
                        boolean m2FromInterface = m2.getDeclaringClass().isInterface();
                        if (m1FromInterface && !m2FromInterface) return 1;
                        if (!m1FromInterface && m2FromInterface) return -1;
                        return 0;
                    })
                    .findFirst();
        }

        // 移除不再需要的实体类列表

        /**
         * 解析参数
         */
        Object[] parseArguments(Class<?>[] paramTypes) {
            try {
                if (args instanceof Object[]) {
                    Object[] argsArray = (Object[]) args;
                    Object[] result = new Object[argsArray.length];
                    for (int i = 0; i < argsArray.length; i++) {
                        // 如果有 argsType 字段，优先使用 argsType 中指定的类型
                        if (argsType != null && i < argsType.length && argsType[i] != null && !argsType[i].isEmpty()) {
                            try {
                                Class<?> actualType = Class.forName(argsType[i]);
                                result[i] = convertToType(argsArray[i], actualType);
                            } catch (ClassNotFoundException e) {
                                System.err.println("找不到类: " + argsType[i] + ", 使用参数类型: " + paramTypes[i].getName());
                                result[i] = convertToType(argsArray[i], paramTypes[i]);
                            }
                        } else {
                            result[i] = convertToType(argsArray[i], paramTypes[i]);
                        }
                    }
                    return result;
                } else if (args instanceof List) {
                    List<?> argsList = (List<?>) args;
                    Object[] result = new Object[argsList.size()];
                    for (int i = 0; i < argsList.size(); i++) {
                        // 如果有 argsType 字段，优先使用 argsType 中指定的类型
                        if (argsType != null && i < argsType.length && argsType[i] != null && !argsType[i].isEmpty()) {
                            try {
                                Class<?> actualType = Class.forName(argsType[i]);
                                result[i] = convertToType(argsList.get(i), actualType);
                            } catch (ClassNotFoundException e) {
                                System.err.println("找不到类: " + argsType[i] + ", 使用参数类型: " + paramTypes[i].getName());
                                result[i] = convertToType(argsList.get(i), paramTypes[i]);
                            }
                        } else {
                            result[i] = convertToType(argsList.get(i), paramTypes[i]);
                        }
                    }
                    return result;
                }
                return new Object[0];
            } catch (Exception e) {
                System.err.println("解析参数失败: " + e.getMessage());
                e.printStackTrace();
                return new Object[0];
            }
        }

        /**
         * 解析返回值
         */
        @SuppressWarnings("unchecked")
        <T> T parseReturnValue(Method method) {
            try {
                Class<?> returnType = method.getReturnType();

                // 如果结果是数组并且需要返回单个对象，取第一个元素
                if (result instanceof List && !returnType.isArray() && !List.class.isAssignableFrom(returnType)) {
                    List<?> resultList = (List<?>) result;
                    if (!resultList.isEmpty()) {
                        return (T) convertToType(resultList.get(0), returnType);
                    }
                    return null;
                }

                // 处理泛型返回值
                if (result instanceof Collection && Collection.class.isAssignableFrom(returnType)) {
                    // 获取方法返回值的泛型类型
                    Type genericReturnType = method.getGenericReturnType();
                    if (genericReturnType instanceof ParameterizedType) {
                        ParameterizedType parameterizedType = (ParameterizedType) genericReturnType;
                        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();

                        if (actualTypeArguments.length > 0) {
                            // 获取集合的泛型参数类型
                            Type actualTypeArgument = actualTypeArguments[0];
                            if (actualTypeArgument instanceof Class) {
                                Class<?> elementType = (Class<?>) actualTypeArgument;
                                return (T) convertCollectionWithGenericType((Collection<?>) result, returnType, elementType);
                            }
                        }
                    }
                }

                // 处理其他类型
                return (T) convertToType(result, returnType);
            } catch (Exception e) {
                System.err.println("解析返回值失败: " + e.getMessage());
                e.printStackTrace();
                return null;
            }
        }

        /**
         * 通用类型转换方法
         * 将源对象转换为目标类型
         */
        @SuppressWarnings("unchecked")
        private <T> T convertToType(Object source, Class<T> targetType) {
            // 处理null值
            if (source == null) {
                return null;
            }

            // 处理基本类型和包装类型
            if (targetType.isPrimitive() ||
                Number.class.isAssignableFrom(targetType) ||
                String.class.equals(targetType) ||
                Boolean.class.equals(targetType) ||
                Character.class.equals(targetType)) {
                return mapper.convertValue(source, targetType);
            }

            // 处理集合类型
            if (Collection.class.isAssignableFrom(targetType)) {
                if (source instanceof Collection) {
                    return (T) convertCollection((Collection<?>) source, targetType);
                }
            }

            // 处理Map类型
            if (Map.class.isAssignableFrom(targetType)) {
                return mapper.convertValue(source, targetType);
            }

            // 处理实体类对象
            if (source instanceof java.util.LinkedHashMap) {
                // 尝试从 argsType 中获取实际类型
                if (((java.util.LinkedHashMap<?, ?>) source).containsKey("argsType")) {
                    Object argsTypeObj = ((java.util.LinkedHashMap<?, ?>) source).get("argsType");
                    if (argsTypeObj instanceof List && !((List<?>) argsTypeObj).isEmpty()) {
                        String className = ((List<?>) argsTypeObj).get(0).toString();
                        try {
                            Class<?> actualType = Class.forName(className);
                            return (T) mapper.convertValue(source, actualType);
                        } catch (ClassNotFoundException e) {
                            System.err.println("找不到类: " + className + ", 使用目标类型: " + targetType.getName());
                        }
                    }
                }
            }

            // 处理List泛型类型
            if (List.class.isAssignableFrom(targetType)) {
                if (source instanceof Collection) {
                    Collection<?> sourceCollection = (Collection<?>) source;
                    List<Object> resultList = new ArrayList<>();
                    for (Object item : sourceCollection) {
                        resultList.add(item);
                    }
                    return (T) resultList;
                } else if (source instanceof Object[]) {
                    Object[] sourceArray = (Object[]) source;
                    List<Object> resultList = new ArrayList<>();
                    for (Object item : sourceArray) {
                        resultList.add(item);
                    }
                    return (T) resultList;
                }
            }

            // 默认转换
            return mapper.convertValue(source, targetType);
        }

        /**
         * 转换集合类型
         */
        private Collection<?> convertCollection(Collection<?> collection, Class<?> collectionType) {
            // 如果是空集合，直接返回空ArrayList
            if (collection.isEmpty()) {
                return new ArrayList<>();
            }

            // 尝试确定集合元素类型
            if (collection.iterator().next() instanceof java.util.LinkedHashMap) {
                try {
                    List<Object> convertedList = new ArrayList<>();
                    for (Object item : collection) {
                        if (item instanceof java.util.LinkedHashMap) {
                            java.util.LinkedHashMap<String, Object> map = (java.util.LinkedHashMap<String, Object>) item;

                            // 尝试从 argsType 中获取实际类型
                            if (map.containsKey("argsType")) {
                                Object argsTypeObj = map.get("argsType");
                                if (argsTypeObj instanceof List && !((List<?>) argsTypeObj).isEmpty()) {
                                    String className = ((List<?>) argsTypeObj).get(0).toString();
                                    try {
                                        Class<?> actualType = Class.forName(className);
                                        Object converted = mapper.convertValue(item, actualType);
                                        convertedList.add(converted);
                                        continue;
                                    } catch (ClassNotFoundException e) {
                                        System.err.println("找不到类: " + className);
                                    }
                                }
                            }

                            // 如果无法确定类型，保留原始对象
                            convertedList.add(item);
                        } else {
                            convertedList.add(item);
                        }
                    }
                    return convertedList;
                } catch (Exception e) {
                    System.err.println("转换集合失败: " + e.getMessage());
                }
            }

            // 默认转换为ArrayList
            List<Object> resultList = new ArrayList<>();
            for (Object item : collection) {
                resultList.add(item);
            }
            return resultList;
        }

        /**
         * 转换集合类型，并处理泛型
         */
        private Collection<?> convertCollectionWithGenericType(Collection<?> collection, Class<?> collectionType, Class<?> elementType) {
            // 如果是空集合，直接返回空ArrayList
            if (collection.isEmpty()) {
                return new ArrayList<>();
            }

            try {
                List<Object> convertedList = new ArrayList<>();
                for (Object item : collection) {
                    if (item instanceof java.util.LinkedHashMap) {
                        // 将LinkedHashMap转换为实际的泛型类型
                        Object converted = mapper.convertValue(item, elementType);
                        convertedList.add(converted);
                    } else {
                        // 如果不是LinkedHashMap，尝试直接转换
                        if (elementType.isInstance(item)) {
                            convertedList.add(item);
                        } else {
                            // 尝试使用Jackson转换
                            Object converted = mapper.convertValue(item, elementType);
                            convertedList.add(converted);
                        }
                    }
                }
                return convertedList;
            } catch (Exception e) {
                System.err.println("转换泛型集合失败: " + e.getMessage());
                e.printStackTrace();

                // 失败时返回原始集合
                return collection;
            }
        }

        // 移除不再需要的方法，因为我们现在使用 argsType 字段来获取参数的实际类型
    }

    /**
     * 自定义异常
     */
    static class MockSetupException extends RuntimeException {
        public MockSetupException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    public static void main(String[] args) {
        //动态打桩示例
        BusinessApiConfigServiceImpl businessApiConfigService = MockDataStubBuilder.buildMock(BusinessApiConfigServiceImpl.class);
        System.out.println(businessApiConfigService.getApiConfigByMetricId(1));
        //静态打桩示例
        HttpRequest httpRequest = Mockito.mock(HttpRequest.class);
        System.out.println(httpRequest.execute());
        //静态打桩示例
        HttpResponse httpResponse = MockDataStubBuilder.buildMock(HttpResponse.class);
        System.out.println(httpResponse.body());
    }
}