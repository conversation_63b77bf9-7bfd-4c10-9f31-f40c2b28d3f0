package com.semptian.service;

import com.semptian.config.ScheduledTaskConfig;
import com.semptian.entity.OpsApiAccessResultDifference;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 定时任务服务测试类
 * 使用Java 8语法
 *
 * <AUTHOR>
 * @since 2025/1/21
 */
@RunWith(MockitoJUnitRunner.class)
public class ScheduledTaskServiceTest {

    @Mock
    private InterSystemDataCompareService interSystemDataCompareService;

    @Mock
    private DetailExportService detailExportService;

    @Mock
    private BusinessMetricConfigService businessMetricConfigService;

    @Mock
    private ScheduledTaskConfig scheduledTaskConfig;

    @InjectMocks
    private ScheduledTaskService scheduledTaskService;

    @Before
    public void setUp() {
        // 设置默认配置
        when(scheduledTaskConfig.isEnabled()).thenReturn(true);
        when(scheduledTaskConfig.getCron()).thenReturn("0 0 12 * * ?");
        when(scheduledTaskConfig.getDescription()).thenReturn("测试任务");
    }

    @Test
    public void testManualTriggerDailyTasks() {
        // 准备测试数据
        List<Integer> metricIds = Arrays.asList(1, 2, 3);
        List<OpsApiAccessResultDifference> differences = Collections.emptyList();
        
        // 设置Mock行为
        when(businessMetricConfigService.getAllMetricIds()).thenReturn(metricIds);
        when(interSystemDataCompareService.compareResult(anyInt(), any(LocalDate.class), any(LocalDate.class), isNull()))
                .thenReturn(differences);
        when(detailExportService.exportHBaseDetail(isNull(), any(LocalDate.class), any(LocalDate.class)))
                .thenReturn("导出成功");

        // 执行测试
        scheduledTaskService.manualTriggerDailyTasks();

        // 验证调用
        verify(businessMetricConfigService, times(1)).getAllMetricIds();
        verify(interSystemDataCompareService, times(3)).compareResult(anyInt(), any(LocalDate.class), any(LocalDate.class), isNull());
        verify(detailExportService, times(1)).exportHBaseDetail(isNull(), any(LocalDate.class), any(LocalDate.class));
    }

    @Test
    public void testManualTriggerTasksForDate() {
        // 准备测试数据
        LocalDate targetDate = LocalDate.of(2025, 1, 20);
        List<Integer> metricIds = Arrays.asList(1, 2);
        List<OpsApiAccessResultDifference> differences = Collections.emptyList();
        
        // 设置Mock行为
        when(businessMetricConfigService.getAllMetricIds()).thenReturn(metricIds);
        when(interSystemDataCompareService.compareResult(anyInt(), any(LocalDate.class), any(LocalDate.class), isNull()))
                .thenReturn(differences);
        when(detailExportService.exportHBaseDetail(isNull(), any(LocalDate.class), any(LocalDate.class)))
                .thenReturn("导出成功");

        // 执行测试
        scheduledTaskService.manualTriggerTasksForDate(targetDate);

        // 验证调用
        verify(businessMetricConfigService, times(1)).getAllMetricIds();
        verify(interSystemDataCompareService, times(2)).compareResult(anyInt(), any(LocalDate.class), any(LocalDate.class), isNull());
        verify(detailExportService, times(1)).exportHBaseDetail(isNull(), any(LocalDate.class), any(LocalDate.class));
    }

    @Test
    public void testGetTaskConfig() {
        // 执行测试
        ScheduledTaskConfig config = scheduledTaskService.getTaskConfig();

        // 验证结果
        assert config != null;
        assert config.equals(scheduledTaskConfig);
    }

    @Test
    public void testIsTaskEnabled() {
        // 执行测试
        boolean enabled = scheduledTaskService.isTaskEnabled();

        // 验证结果
        assert enabled == true;
        verify(scheduledTaskConfig, times(1)).isEnabled();
    }

    @Test
    public void testExecuteDaily12PMTasksWhenDisabled() {
        // 设置任务为禁用状态
        when(scheduledTaskConfig.isEnabled()).thenReturn(false);

        // 执行测试
        scheduledTaskService.executeDaily12PMTasks();

        // 验证没有调用业务方法
        verify(businessMetricConfigService, times(0)).getAllMetricIds();
        verify(interSystemDataCompareService, times(0)).compareResult(anyInt(), any(LocalDate.class), any(LocalDate.class), isNull());
        verify(detailExportService, times(0)).exportHBaseDetail(isNull(), any(LocalDate.class), any(LocalDate.class));
    }
}
