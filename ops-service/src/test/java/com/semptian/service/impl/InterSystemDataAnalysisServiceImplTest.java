package com.semptian.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.semptian.component.I18nUtils;
import com.semptian.entity.BusinessApiConfig;
import com.semptian.entity.BusinessMetricConfig;
import com.semptian.entity.BusinessSysConfig;
import com.semptian.entity.OpsApiAccessResult;
import com.semptian.model.metrics.DailyTrendItem;
import com.semptian.model.metrics.DetailItem;
import com.semptian.model.metrics.HomePageTop10;
import com.semptian.model.metrics.MetricResponse;
import com.semptian.service.BusinessApiConfigService;
import com.semptian.service.BusinessMetricConfigService;
import com.semptian.service.BusinessSysConfigService;
import com.semptian.service.OpsApiAccessResultService;
import com.semptian.service.common.handler.MessageSourceHandler;
import com.semptian.service.constants.TestConstants;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequest.class, HttpResponse.class, I18nUtils.class})
@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
@Slf4j
public class InterSystemDataAnalysisServiceImplTest {

    @InjectMocks
    private InterSystemDataAnalysisServiceImpl dataAnalysisService;

    @Mock
    private BusinessSysConfigService sysConfigService;

    @Mock
    private BusinessApiConfigService apiConfigService;

    @Mock
    private BusinessMetricConfigService businessMetricConfigService;

    @Mock
    private OpsApiAccessResultService opsApiAccessResultService;

    @Mock
    private MessageSourceHandler handler;

    private List<BusinessSysConfig> businessSysConfigs;
    private LocalDate startDate;
    private LocalDate endDate;

    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        businessSysConfigs = createBusinessSysConfigs(true);
        startDate = LocalDate.now().minusDays(7);
        endDate = LocalDate.now();

        Field handlerField = I18nUtils.class.getDeclaredField("handler");
        handlerField.setAccessible(true);
        //null表示为静态字段
        handlerField.set(null, handler);

        // Mock system config service
        when(sysConfigService.getSysConfigList(anyInt(), anyInt(), anyString(), anyString(), any())).thenReturn(new Page<BusinessSysConfig>().setRecords(businessSysConfigs));

        // Mock HTTP request for API calls
        PowerMockito.mockStatic(HttpRequest.class);
        List<OpsApiAccessResult> opsApiAccessResults = new ArrayList<>();
        OpsApiAccessResult opsApiAccessResult = OpsApiAccessResult.builder().build();
        opsApiAccessResult.setApiPath(TestConstants.Metric1Constants.ARCHIVE_API_PATH);
        opsApiAccessResult.setParams(JSONObject.parseObject(TestConstants.Metric1Constants.ARCHIVE_PARAMS));
        opsApiAccessResult.setStatCount(100L);
        opsApiAccessResult.setSystemName(TestConstants.ARCHIVE_NAME);
        opsApiAccessResult.setAccount("testAccount");
        opsApiAccessResult.setStatDate(new Date());
        opsApiAccessResult.setMetricId("1");
        opsApiAccessResult.setKv(true);
        opsApiAccessResult.setCostTime(100);
        opsApiAccessResults.add(opsApiAccessResult);

        when(opsApiAccessResultService.getById(anyString())).thenReturn(opsApiAccessResult);

        // Mock OpsApiAccessResultService
        when(opsApiAccessResultService.calculateDiffRate(anyLong(), any(LocalDate.class), any(LocalDate.class))).thenReturn(0.25);
        when(opsApiAccessResultService.calculateAccountDiffRate(anyLong(), anyString(), any(LocalDate.class), any(LocalDate.class), any())).thenReturn(25.0);
        doReturn(new Page<OpsApiAccessResult>().setRecords(opsApiAccessResults).setTotal(5L).setSize(20).setCurrent(1)).when(opsApiAccessResultService).pageQuery(anyLong(), any(Page.class), any(), any(), anyBoolean(), any(LocalDate.class), any(LocalDate.class));


        // Mock BusinessMetricConfigService
        BusinessMetricConfig metricConfig = BusinessMetricConfig.builder().id(1L).metricName("活跃次数").metricModelName("Email数据差异分析").compareSystems("综合搜索,全息档案,案件管理").threshold(0.5).description("Test Description").status(BusinessMetricConfig.Status.ACTIVE).build();
        when(businessMetricConfigService.getById(any())).thenReturn(metricConfig);

        Page<BusinessMetricConfig> metricConfigPage = new Page<>();
        List<BusinessMetricConfig> metricConfigs = new ArrayList<>();
        metricConfigs.add(metricConfig);
        metricConfigs.add(BusinessMetricConfig.builder().id(2L).status(BusinessMetricConfig.Status.ACTIVE).compareSystems("综合搜索,全息档案,案件管理").threshold(0.5).metricModelName("Email数据差异分析").metricName("活跃次数").build());
        metricConfigPage.setRecords(metricConfigs);
        when(businessMetricConfigService.getMetricConfigList(anyInt(), anyInt(), anyString(), any(), anyString())).thenReturn(metricConfigPage);
    }

    @Test
    public void getMetrics_ShouldReturnPaginatedResults() throws NoSuchFieldException, IllegalAccessException {
        // Prepare test data
        mockApiConfigsForMetric1();

        // Mock HTTP response for metrics
        JSONObject mockResponse = new JSONObject();
        mockResponse.put("code", 200);
        JSONObject data = new JSONObject();
        data.put("total", 2);
        data.put("list", new ArrayList<>());
        mockResponse.put("data", data);
        HttpRequest mockRequest = mockHttpRequest(mockResponse);
        when(HttpRequest.get(anyString())).thenReturn(mockRequest);
        when(HttpRequest.post(anyString())).thenReturn(mockRequest);

        when(I18nUtils.getMessage(org.mockito.ArgumentMatchers.contains("活跃次数"))).thenReturn("activeTimes");
        when(I18nUtils.getMessage(org.mockito.ArgumentMatchers.contains("Email数据差异分析"))).thenReturn("emailDataAnalysis");
        when(I18nUtils.getMessage(org.mockito.ArgumentMatchers.contains("综合搜索"))).thenReturn("search");
        when(I18nUtils.getMessage(org.mockito.ArgumentMatchers.contains("全息档案"))).thenReturn("archive");
        when(I18nUtils.getMessage(org.mockito.ArgumentMatchers.contains("案件管理"))).thenReturn("case");

        // Execute test
        List<MetricResponse> result = dataAnalysisService.getMetrics(1, 10, "type1", startDate, endDate);

        // Verify results
        assertNotNull(result);
        assertEquals(2, result.size());
    }

    @Test
    public void getDetails_ShouldHandleSortParameters() {
        // Prepare test data
        String metricId = "1";
        String account = "testAccount";
        String sort = "timestamp";
        boolean desc = true;
        mockApiConfigsForMetric1();

        // Mock HTTP response for details
        JSONObject mockResponse = new JSONObject();
        mockResponse.put("code", 200);
        JSONObject data = new JSONObject();
        data.put("total", 5);
        data.put("list", new ArrayList<>());
        mockResponse.put("data", data);
        HttpRequest mockRequest = mockHttpRequest(mockResponse);
        when(HttpRequest.get(anyString())).thenReturn(mockRequest);

        // Execute test
        Page<DetailItem> result = dataAnalysisService.getDetails(metricId, 1, 20, account, sort, desc, startDate, endDate);

        // Verify results
        assertNotNull(result);
        assertEquals(1L, result.getTotal());
        assertEquals(20, result.getSize());
        assertEquals(1, result.getCurrent());
    }

    @Test
    public void getHomepagePanel_ShouldReturnDifferentStructures() {
        // Prepare test data
        int panelId = 1;
        mockApiConfigsForMetric1();

        // Mock HTTP response for panel data
        JSONObject mockResponse = new JSONObject();
        mockResponse.put("code", 200);
        JSONObject data = new JSONObject();
        data.put("panelId", panelId);
        data.put("metrics", new ArrayList<>());
        mockResponse.put("data", data);
        HttpRequest mockRequest = mockHttpRequest(mockResponse);
        when(HttpRequest.get(anyString())).thenReturn(mockRequest);

        // Execute test
        Object result = dataAnalysisService.getHomepagePanel(startDate, endDate, panelId);

        // Verify results
        assertNotNull(result);
        // The result should be a HomePageTop10 object, not a JSONObject
        HomePageTop10 homePageTop10 = (HomePageTop10) result;
        assertNotNull(homePageTop10.getMetricName());
        assertNotNull(homePageTop10.getDetailItemPage());
    }

    @Test
    public void getDailyTrend_ShouldReturnTrendData() {
        // Prepare test data
        String metricId = "1";
        mockApiConfigsForMetric1();

        // Mock HTTP response for trend data
        JSONObject mockResponse = new JSONObject();
        mockResponse.put("code", 200);
        List<JSONObject> trendData = new ArrayList<>();
        for (int i = 0; i < 8; i++) {
            JSONObject item = new JSONObject();
            item.put("date", startDate.plusDays(i).toString());
            item.put("overAllDiffRate", 100.0 + i);
            trendData.add(item);
        }
        mockResponse.put("data", trendData);
        HttpRequest mockRequest = mockHttpRequest(mockResponse);
        when(HttpRequest.get(anyString())).thenReturn(mockRequest);

        // Execute test
        List<DailyTrendItem> result = dataAnalysisService.getDailyTrend(metricId, startDate, endDate);

        // Verify results
        assertNotNull(result);
        assertEquals(8, result.size());
        for (int i = 0; i < result.size(); i++) {
            DailyTrendItem item = result.get(i);
            assertEquals(startDate.plusDays(i), item.getDate());
            assertEquals(Double.valueOf(0.25), item.getOverAllDiffRate());
        }
    }

    private List<BusinessSysConfig> createBusinessSysConfigs(boolean isEnable) {
        List<BusinessSysConfig> businessSysConfigs = new ArrayList<>();

        // 1. 综合搜索
        BusinessSysConfig searchConfig = BusinessSysConfig.builder().build();
        searchConfig.setSysType(BusinessSysConfig.SysType.SEARCH);
        searchConfig.setAppid(TestConstants.SEARCH_APPID);
        searchConfig.setBaseUrl(TestConstants.SEARCH_BASE_URL);
        searchConfig.setAuthConfig((new JSONObject().fluentPut("account", TestConstants.PORTAL_ACCOUNT_SEARCH).fluentPut("password", TestConstants.PORTAL_PASSWORD_SEARCH).fluentPut("auth_url", TestConstants.PORTAL_URL)).toString());
        searchConfig.setEnable(true);
        searchConfig.setSysName(TestConstants.SEARCH_NAME);
        businessSysConfigs.add(searchConfig);

        // 2. 全息档案
        BusinessSysConfig archiveConfig = BusinessSysConfig.builder().build();
        archiveConfig.setSysType(BusinessSysConfig.SysType.ARCHIVE);
        archiveConfig.setAppid(TestConstants.ARCHIVE_APPID);
        archiveConfig.setBaseUrl(TestConstants.ARCHIVE_BASE_URL);
        archiveConfig.setAuthConfig((new JSONObject().fluentPut("account", TestConstants.PORTAL_ACCOUNT_ARCHIVE).fluentPut("password", TestConstants.PORTAL_PASSWORD_ARCHIVE).fluentPut("auth_url", TestConstants.PORTAL_URL)).toString());
        archiveConfig.setEnable(true);
        archiveConfig.setSysName(TestConstants.ARCHIVE_NAME);
        businessSysConfigs.add(archiveConfig);

        // 3. 案件管理
        if (isEnable) {
            BusinessSysConfig caseConfig = BusinessSysConfig.builder().build();
            caseConfig.setSysType(BusinessSysConfig.SysType.CASE);
            caseConfig.setAppid(TestConstants.CASE_APPID);
            caseConfig.setBaseUrl(TestConstants.CASE_BASE_URL);
            caseConfig.setAuthConfig((new JSONObject().fluentPut("account", TestConstants.PORTAL_ACCOUNT_CASE).fluentPut("password", TestConstants.PORTAL_PASSWORD_CASE).fluentPut("auth_url", TestConstants.PORTAL_URL)).toString());
            caseConfig.setEnable(true);
            caseConfig.setSysName(TestConstants.CASE_NAME);
            businessSysConfigs.add(caseConfig);
        }

        return businessSysConfigs;
    }

    private void mockApiConfigsForMetric1() {
        List<BusinessApiConfig> apiConfigs = new ArrayList<>();

        BusinessApiConfig apiConfig1 = BusinessApiConfig.builder().build();
        apiConfig1.setMetricId(TestConstants.Metric1Constants.METRIC_ID);
        apiConfig1.setSystemName(TestConstants.ARCHIVE_NAME);
        apiConfig1.setMethod(BusinessApiConfig.Method.GET);
        apiConfig1.setApiPath(TestConstants.Metric1Constants.ARCHIVE_API_PATH);
        String params1 = TestConstants.Metric1Constants.ARCHIVE_PARAMS;
        apiConfig1.setParamsTemplate(JSONObject.parseObject(params1));
        String responseMapping1 = TestConstants.Metric1Constants.ARCHIVE_RESPONSE_MAPPING;
        apiConfig1.setResponseMapping(JSONObject.parseObject(responseMapping1));
        apiConfigs.add(apiConfig1);

        BusinessApiConfig apiConfig2 = BusinessApiConfig.builder().build();
        apiConfig2.setMetricId(TestConstants.Metric1Constants.METRIC_ID);
        apiConfig2.setSystemName(TestConstants.CASE_NAME);
        apiConfig2.setMethod(BusinessApiConfig.Method.GET);
        apiConfig2.setApiPath(TestConstants.Metric1Constants.CASE_API_PATH);
        String params2 = TestConstants.Metric1Constants.CASE_PARAMS;
        apiConfig2.setParamsTemplate(JSONObject.parseObject(params2));
        String responseMapping2 = TestConstants.Metric1Constants.CASE_RESPONSE_MAPPING;
        apiConfig2.setResponseMapping(JSONObject.parseObject(responseMapping2));
        apiConfigs.add(apiConfig2);

        when(apiConfigService.getApiConfigByMetricId(anyInt())).thenReturn(apiConfigs);
    }

    private HttpRequest mockHttpRequest(JSONObject response) {
        HttpRequest mockRequest = PowerMockito.mock(HttpRequest.class);
        HttpResponse mockResponse = PowerMockito.mock(HttpResponse.class);

        when(mockRequest.header(anyString(), anyString())).thenReturn(mockRequest);
        when(mockRequest.body(anyString())).thenReturn(mockRequest);
        when(mockRequest.execute()).thenReturn(mockResponse);
        when(mockResponse.body()).thenReturn(response.toJSONString());
        when(mockResponse.getStatus()).thenReturn(200);
        when(mockResponse.isOk()).thenReturn(true);

        return mockRequest;
    }
}