package com.semptian.service.impl;

import com.semptian.component.FileSystemUtil;
import com.semptian.entity.DetailFieldConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Comparator;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DetailExportServiceImpl集成测试类 - 测试完整的CSV文件生成流程
 *
 * <AUTHOR>
 * @date 2025/05/27
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class DetailExportServiceImplIntegrationTest {

    @InjectMocks
    private DetailExportServiceImpl detailExportService;

    @Mock
    private FileSystemUtil fileSystemUtil;

    private Method generateCsvFileMethod;
    private String testBasePath;

    @Before
    public void setUp() throws Exception {
        // 设置测试路径
        testBasePath = System.getProperty("java.io.tmpdir") + "/csv_integration_test";

        // 通过反射获取私有方法
        generateCsvFileMethod = DetailExportServiceImpl.class.getDeclaredMethod("generateCsvFile",
                Integer.class, String.class, List.class, List.class);
        generateCsvFileMethod.setAccessible(true);
    }

    /**
     * 测试完整的CSV文件生成流程 - 包含各种数据类型和特殊字符
     */
    @Test
    public void testCompleteCSVGeneration() throws Exception {
        // 准备测试数据
        Integer metricId = 123;
        String protocolType = "email";
        List<Map<String, Object>> hbaseData = createComplexTestData();
        List<DetailFieldConfig> fieldConfigs = createComplexFieldConfigs();

        // 创建真实的文件路径
        Path testFilePath = createRealTestFilePath(metricId, protocolType);
        when(fileSystemUtil.createExportFilePath(anyInt(), anyString(), anyLong(), any(LocalDate.class))).thenReturn(testFilePath);

        // 执行CSV生成
        LocalDate testDate = LocalDate.of(2024, 5, 9);
        generateCsvFileMethod.invoke(detailExportService, metricId, protocolType, hbaseData, fieldConfigs, testDate);

        // 验证文件是否生成
        assertTrue("CSV文件应该被创建", Files.exists(testFilePath));

        // 读取并验证文件内容
        List<String> lines = Files.readAllLines(testFilePath);
        assertFalse("文件不应为空", lines.isEmpty());

        // 验证头部
        String header = lines.get(0);
        log.info("CSV头部: {}", header);
        assertTrue("头部应包含通用字段", header.contains("监控指标"));
        assertTrue("头部应包含协议字段", header.contains("用户名"));
        assertTrue("头部应包含特殊字段", header.contains("包含,逗号的字段"));

        // 验证数据行
        assertTrue("应至少有一行数据", lines.size() > 1);
        String dataRow = lines.get(1);
        log.info("第一行数据: {}", dataRow);

        // 验证特殊字符处理
        assertTrue("应正确处理包含逗号的值", dataRow.contains("\"value,with,comma\""));
        assertTrue("应正确处理包含引号的值", dataRow.contains("\"value\"\"with\"\"quote\""));

        log.info("完整CSV生成测试通过，生成了{}行数据", lines.size() - 1);
    }

    /**
     * 测试大量数据的CSV生成性能
     */
    @Test
    public void testLargeDataCSVGeneration() throws Exception {
        Integer metricId = 456;
        String protocolType = "phone";

        // 创建10000条测试数据
        List<Map<String, Object>> hbaseData = createLargeTestData(10000);
        List<DetailFieldConfig> fieldConfigs = createSimpleFieldConfigs();

        Path testFilePath = createRealTestFilePath(metricId, protocolType);
        when(fileSystemUtil.createExportFilePath(anyInt(), anyString(), anyLong(), any(LocalDate.class))).thenReturn(testFilePath);

        // 测量性能
        long startTime = System.currentTimeMillis();
        LocalDate testDate = LocalDate.of(2024, 5, 9);
        generateCsvFileMethod.invoke(detailExportService, metricId, protocolType, hbaseData, fieldConfigs, testDate);
        long endTime = System.currentTimeMillis();

        // 验证结果
        assertTrue("CSV文件应该被创建", Files.exists(testFilePath));

        List<String> lines = Files.readAllLines(testFilePath);
        assertEquals("应生成正确数量的行", 10001, lines.size()); // 10000数据行 + 1头部行

        long fileSize = Files.size(testFilePath);
        log.info("大量数据CSV生成完成 - 数据量: 10000条, 耗时: {}ms, 文件大小: {}KB",
                endTime - startTime, fileSize / 1024);

        // 性能断言（根据实际情况调整）
        assertTrue("生成10000条数据应在合理时间内完成", endTime - startTime < 10000);
    }

    /**
     * 测试空数据的CSV生成
     */
    @Test
    public void testEmptyDataCSVGeneration() throws Exception {
        Integer metricId = 789;
        String protocolType = "radius";
        List<Map<String, Object>> hbaseData = new ArrayList<>();
        List<DetailFieldConfig> fieldConfigs = createSimpleFieldConfigs();

        Path testFilePath = createRealTestFilePath(metricId, protocolType);
        when(fileSystemUtil.createExportFilePath(anyInt(), anyString(), anyLong(), any(LocalDate.class))).thenReturn(testFilePath);

        // 执行CSV生成
        LocalDate testDate = LocalDate.of(2024, 5, 9);
        generateCsvFileMethod.invoke(detailExportService, metricId, protocolType, hbaseData, fieldConfigs, testDate);

        // 验证结果
        assertTrue("CSV文件应该被创建", Files.exists(testFilePath));

        List<String> lines = Files.readAllLines(testFilePath);
        assertEquals("空数据应只有头部行", 1, lines.size());

        String header = lines.get(0);
        assertTrue("头部应包含通用字段", header.contains("监控指标"));

        log.info("空数据CSV生成测试通过");
    }

    /**
     * 测试特殊字符和编码处理
     */
    @Test
    public void testSpecialCharactersAndEncoding() throws Exception {
        Integer metricId = 999;
        String protocolType = "im";

        // 创建包含各种特殊字符的数据
        List<Map<String, Object>> hbaseData = createSpecialCharacterData();
        List<DetailFieldConfig> fieldConfigs = createSimpleFieldConfigs();

        Path testFilePath = createRealTestFilePath(metricId, protocolType);
        when(fileSystemUtil.createExportFilePath(anyInt(), anyString(), anyLong(), any(LocalDate.class))).thenReturn(testFilePath);

        // 执行CSV生成
        LocalDate testDate = LocalDate.of(2024, 5, 9);
        generateCsvFileMethod.invoke(detailExportService, metricId, protocolType, hbaseData, fieldConfigs, testDate);

        // 验证文件编码和内容
        assertTrue("CSV文件应该被创建", Files.exists(testFilePath));

        // 使用UTF-8读取文件
        List<String> lines = Files.readAllLines(testFilePath);
        assertTrue("应至少有一行数据", lines.size() > 1);

        String dataRow = lines.get(1);
        log.info("特殊字符数据行: {}", dataRow);

        // 验证中文字符处理
        assertTrue("应正确处理中文字符", dataRow.contains("测试中文"));
        // 验证emoji处理
        assertTrue("应正确处理emoji", dataRow.contains("😀"));


        log.info("特殊字符和编码测试通过");
    }

    /**
     * 创建复杂的测试数据
     */
    private List<Map<String, Object>> createComplexTestData() {
        List<Map<String, Object>> data = new ArrayList<>();

        Map<String, Object> row1 = new HashMap<>();
        row1.put("account", "<EMAIL>");
        row1.put("用户名", "张三");
        row1.put("密码", "password123");
        row1.put("包含,逗号的字段", "value,with,comma");
        row1.put("包含\"引号的字段", "value\"with\"quote");
        row1.put("包含换行的字段", "line1\nline2");
        row1.put("数字字段", 12345);
        row1.put("浮点字段", 123.45);
        row1.put("布尔字段", true);
        row1.put("日期字段", LocalDateTime.now());
        row1.put("空字段", null);
        data.add(row1);

        Map<String, Object> row2 = new HashMap<>();
        row2.put("account", "<EMAIL>");
        row2.put("用户名", "李四");
        row2.put("密码", "");
        row2.put("包含,逗号的字段", "normal value");
        row2.put("包含\"引号的字段", "another\"\"value");
        row2.put("包含换行的字段", "single line");
        row2.put("数字字段", 0);
        row2.put("浮点字段", -99.99);
        row2.put("布尔字段", false);
        row2.put("日期字段", null);
        row2.put("空字段", "");
        data.add(row2);

        return data;
    }

    /**
     * 创建复杂的字段配置
     */
    private List<DetailFieldConfig> createComplexFieldConfigs() {
        List<DetailFieldConfig> configs = new ArrayList<>();

        String[] fieldNames = {
            "用户名", "密码", "包含,逗号的字段", "包含\"引号的字段",
            "包含换行的字段", "数字字段", "浮点字段", "布尔字段", "日期字段", "空字段"
        };

        for (String fieldName : fieldNames) {
            DetailFieldConfig config = new DetailFieldConfig();
            config.setFieldName(fieldName);
            configs.add(config);
        }

        return configs;
    }

    /**
     * 创建大量测试数据
     */
    private List<Map<String, Object>> createLargeTestData(int count) {
        List<Map<String, Object>> data = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            Map<String, Object> row = new HashMap<>();
            row.put("account", "user" + i + "@example.com");
            row.put("field1", "value1_" + i);
            row.put("field2", "value2_" + i);
            row.put("field3", i);
            data.add(row);
        }

        return data;
    }

    /**
     * 创建包含特殊字符的数据
     */
    private List<Map<String, Object>> createSpecialCharacterData() {
        List<Map<String, Object>> data = new ArrayList<>();

        Map<String, Object> row = new HashMap<>();
        row.put("account", "<EMAIL>");
        row.put("field1", "测试中文");
        row.put("field2", "😀🎉🚀"); // emoji
        row.put("field3", "line1\nline2\rline3"); // 换行和回车
        row.put("field4", "tab\there"); // 制表符
        row.put("field5", "quote\"and'apostrophe"); // 引号
        data.add(row);

        return data;
    }

    /**
     * 创建简单的字段配置
     */
    private List<DetailFieldConfig> createSimpleFieldConfigs() {
        List<DetailFieldConfig> configs = new ArrayList<>();

        for (int i = 1; i <= 5; i++) {
            DetailFieldConfig config = new DetailFieldConfig();
            config.setFieldName("field" + i);
            configs.add(config);
        }

        return configs;
    }

    /**
     * 创建真实的测试文件路径
     */
    private Path createRealTestFilePath(Integer metricId, String protocolType) throws IOException {
        Path testDir = Paths.get(testBasePath);
        Files.createDirectories(testDir);

        String fileName = protocolType + "_" + metricId + "_" + System.currentTimeMillis() + ".csv";
        return testDir.resolve(fileName);
    }

    /**
     * 清理测试文件
     */
    @org.junit.After
    public void tearDown() {
        try {
            Path testDir = Paths.get(testBasePath);
            if (Files.exists(testDir)) {
                Files.walk(testDir)
                        .sorted(Comparator.reverseOrder())
                        .map(Path::toFile)
                        .forEach(File::delete);
            }
        } catch (Exception e) {
            log.warn("清理测试文件失败: {}", e.getMessage());
        }
    }
}
