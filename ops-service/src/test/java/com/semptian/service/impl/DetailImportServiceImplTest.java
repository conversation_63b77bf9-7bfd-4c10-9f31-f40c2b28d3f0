package com.semptian.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.semptian.entity.DetailImportTask;
import com.semptian.mapper.DetailImportTaskMapper;
import com.semptian.service.DetailImportService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;

import static org.junit.Assert.*;

/**
 * 明细导入服务测试类
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class DetailImportServiceImplTest {

    @Resource
    private DetailImportService detailImportService;

    @Resource
    private DetailImportTaskMapper detailImportTaskMapper;

    @Test
    public void testImportCsvToDoris() {
        try {
            // 测试导入指定日期的CSV文件
            LocalDate targetDate = LocalDate.now().minusDays(1);
            Integer metricId = 1;
            String tableName = "ops_detail_data_test";

            String result = detailImportService.importCsvToDoris(metricId, targetDate, tableName);
            log.info("导入结果: {}", result);

        } catch (Exception e) {
            log.error("测试导入CSV失败", e);
        }
    }

    @Test
    public void testUploadAndImportCsv() {
        try {
            // 创建测试CSV文件内容（通话协议）
            String csvContent = "监控指标,系统,数据层级,表名,协议类型编码,指标账号,action_map,called_number,calling_number,call_type_map,capture_day,capture_time,data_id,data_type_map,duration,end_time,start_time\n" +
                    "1,搜索,HBase,明细表,call,13800138000,呼叫,13900139000,13800138000,呼出,2025-01-20,10:00:00,data001,通话,300,2025-01-20 10:05:00,2025-01-20 10:00:00\n" +
                    "1,搜索,HBase,明细表,call,13800138001,呼叫,13900139001,13800138001,呼入,2025-01-20,11:00:00,data002,通话,180,2025-01-20 11:03:00,2025-01-20 11:00:00\n" +
                    "1,搜索,HBase,明细表,call,13800138002,呼叫,13900139002,13800138002,呼出,2025-01-20,12:00:00,data003,通话,120,2025-01-20 12:02:00,2025-01-20 12:00:00";

            MockMultipartFile file = new MockMultipartFile(
                    "file",
                    "test_call_data.csv",
                    "text/csv",
                    csvContent.getBytes(StandardCharsets.UTF_8)
            );

            Integer metricId = 1;
            String tableName = null; // 使用默认表名，将自动按协议分表

            String result = detailImportService.uploadAndImportCsv(file, metricId, tableName);
            log.info("上传导入结果: {}", result);

            // 等待异步处理完成
            Thread.sleep(5000);

            // 提取任务ID并查询状态
            String taskId = result.substring(result.lastIndexOf(": ") + 2);
            Object status = detailImportService.getImportTaskStatus(taskId);
            log.info("任务状态: {}", status);

        } catch (Exception e) {
            log.error("测试上传导入CSV失败", e);
        }
    }

    @Test
    public void testUploadAndImportMultiProtocolCsv() {
        try {
            // 创建测试CSV文件内容（混合协议）
            String csvContent = "监控指标,系统,数据层级,表名,协议类型编码,指标账号,action_map,called_number,calling_number,mail_from,mail_to,from_id,to_id\n" +
                    "1,搜索,HBase,明细表,call,13800138000,呼叫,13900139000,13800138000,,,\n" +
                    "2,搜索,HBase,明细表,email,<EMAIL>,发送,,,<EMAIL>,<EMAIL>,,\n" +
                    "3,搜索,HBase,明细表,im,qq123456,消息,,,,,qq123456,qq789012";

            MockMultipartFile file = new MockMultipartFile(
                    "file",
                    "test_multi_protocol_data.csv",
                    "text/csv",
                    csvContent.getBytes(StandardCharsets.UTF_8)
            );

            Integer metricId = 1;
            String tableName = null; // 使用默认表名，将自动按协议分表

            String result = detailImportService.uploadAndImportCsv(file, metricId, tableName);
            log.info("混合协议上传导入结果: {}", result);

            // 等待异步处理完成
            Thread.sleep(5000);

            // 提取任务ID并查询状态
            String taskId = result.substring(result.lastIndexOf(": ") + 2);
            Object status = detailImportService.getImportTaskStatus(taskId);
            log.info("混合协议任务状态: {}", status);

        } catch (Exception e) {
            log.error("测试混合协议上传导入CSV失败", e);
        }
    }

    @Test
    public void testGetImportTaskStatus() {
        try {
            // 查询最近的一个任务状态
            DetailImportTask task = detailImportTaskMapper.selectList(null)
                    .stream()
                    .findFirst()
                    .orElse(null);

            if (task != null) {
                Object status = detailImportService.getImportTaskStatus(task.getTaskId());
                log.info("任务状态查询结果: {}", status);
            } else {
                log.info("没有找到任何导入任务");
            }

        } catch (Exception e) {
            log.error("测试查询任务状态失败", e);
        }
    }

    @Test
    public void testImportAllCsvFiles() {
        try {
            // 测试导入所有CSV文件
            LocalDate targetDate = LocalDate.now().minusDays(1);
            String tableName = "ops_detail_data_test";

            String result = detailImportService.importCsvToDoris(null, targetDate, tableName);
            log.info("全量导入结果: {}", result);

        } catch (Exception e) {
            log.error("测试全量导入CSV失败", e);
        }
    }

    @Test
    public void testCreateTestCsvFile() {
        try {
            // 这个测试用于创建测试用的CSV文件
            // 实际使用时，可以通过明细导出功能生成真实的CSV文件
            log.info("请先使用明细导出功能生成CSV文件，然后再测试导入功能");
            log.info("导出API: GET /ops/detail/export/hbase.json?metricId=1&startDate=2025-01-19&endDate=2025-01-19");
            log.info("导入API: POST /ops/detail/import/csv.json?metricId=1&targetDate=2025-01-19&tableName=ops_detail_data_test");

        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }

    @Test
    public void testParseParamsJson() {
        try {
            DetailImportServiceImpl service = new DetailImportServiceImpl();

            // 使用反射访问私有方法
            Method parseParamsJsonMethod = DetailImportServiceImpl.class.getDeclaredMethod("parseParamsJson", String.class);
            parseParamsJsonMethod.setAccessible(true);

            // 测试 JSON 对象
            String jsonObjectStr = "{\"size\":1500,\"query\":{\"bool\":{\"filter\":[]}}}";
            JSONObject result1 = (JSONObject) parseParamsJsonMethod.invoke(service, jsonObjectStr);
            assertNotNull("JSON对象解析结果不应为null", result1);
            assertEquals("size字段值应为1500", Integer.valueOf(1500), result1.getInteger("size"));
            log.info("JSON对象解析测试通过: {}", result1);

            // 测试 JSON 数组
            String jsonArrayStr = "[{\"size\":1500,\"query\":{\"bool\":{\"filter\":[]}}},{\"size\":1000,\"query\":{\"bool\":{\"must\":[]}}}]";
            JSONObject result2 = (JSONObject) parseParamsJsonMethod.invoke(service, jsonArrayStr);
            assertNotNull("JSON数组解析结果不应为null", result2);
            assertTrue("应包含queries字段", result2.containsKey("queries"));
            assertEquals("数组长度应为2", 2, result2.getJSONArray("queries").size());
            log.info("JSON数组解析测试通过: {}", result2);

            // 测试空字符串
            JSONObject result3 = (JSONObject) parseParamsJsonMethod.invoke(service, "");
            assertNull("空字符串解析结果应为null", result3);
            log.info("空字符串解析测试通过");

            // 测试 null
            JSONObject result4 = (JSONObject) parseParamsJsonMethod.invoke(service, (String) null);
            assertNull("null解析结果应为null", result4);
            log.info("null解析测试通过");

            // 测试非 JSON 字符串
            String nonJsonStr = "这不是一个JSON字符串";
            JSONObject result5 = (JSONObject) parseParamsJsonMethod.invoke(service, nonJsonStr);
            assertNotNull("非JSON字符串解析结果不应为null", result5);
            assertTrue("应包含raw字段", result5.containsKey("raw"));
            assertEquals("raw字段值应与原字符串相同", nonJsonStr, result5.getString("raw"));
            log.info("非JSON字符串解析测试通过: {}", result5);

        } catch (Exception e) {
            log.error("测试parseParamsJson失败", e);
            fail("测试parseParamsJson失败: " + e.getMessage());
        }
    }

    @Test
    public void testParseComplexElasticsearchQuery() {
        try {
            DetailImportServiceImpl service = new DetailImportServiceImpl();

            // 使用反射访问私有方法
            Method parseParamsJsonMethod = DetailImportServiceImpl.class.getDeclaredMethod("parseParamsJson", String.class);
            parseParamsJsonMethod.setAccessible(true);

            // 测试复杂的 Elasticsearch DSL 查询数组（类似错误信息中的格式）
            String complexQuery = "[{\"size\":1500,\"query\":{\"bool\":{\"filter\":[{\"range\":{\"capture_timeField\":{\"include_lower\":true,\"include_upper\":true,\"from\":*************,\"boost\":1,\"to\":*************}}}],\"adjust_pure_negative\":true,\"must\":[{\"query_string\":{\"max_determinized_states\":10000,\"auto_generate_synonyms_phrase_query\":true,\"phrase_slop\":2,\"query\":\"auth_accountField.keyword : ********* && auth_typeField : \\\"1020001\\\" \",\"fuzzy_transpositions\":true,\"type\":\"best_fields\",\"fuzzy_prefix_length\":0,\"default_operator\":\"and\",\"fuzzy_max_expansions\":50,\"boost\":1,\"enable_position_increments\":true,\"fields\":[],\"escape\":false}}],\"boost\":1}},\"timeout\":\"60s\",\"aggs\":{\"virtual_account_count\":{\"terms\":{\"size\":500,\"field\":\"main_accountField.keyword\"}}},\"track_total_hits\":true}]";

            JSONObject result = (JSONObject) parseParamsJsonMethod.invoke(service, complexQuery);

            assertNotNull("复杂查询解析结果不应为null", result);
            assertTrue("应包含queries字段", result.containsKey("queries"));
            assertEquals("数组长度应为1", 1, result.getJSONArray("queries").size());

            // 验证查询结构
            JSONObject firstQuery = result.getJSONArray("queries").getJSONObject(0);
            assertEquals("size字段值应为1500", Integer.valueOf(1500), firstQuery.getInteger("size"));
            assertTrue("应包含query字段", firstQuery.containsKey("query"));
            assertTrue("应包含timeout字段", firstQuery.containsKey("timeout"));
            assertTrue("应包含aggs字段", firstQuery.containsKey("aggs"));

            log.info("复杂Elasticsearch查询解析测试通过");
            log.info("解析结果: {}", result.toJSONString());

        } catch (Exception e) {
            log.error("测试复杂Elasticsearch查询解析失败", e);
            fail("测试复杂Elasticsearch查询解析失败: " + e.getMessage());
        }
    }
}
