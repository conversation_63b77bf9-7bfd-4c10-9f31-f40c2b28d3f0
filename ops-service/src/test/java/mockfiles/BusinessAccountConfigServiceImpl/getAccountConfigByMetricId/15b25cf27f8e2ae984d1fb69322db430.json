{"args": [{"id": 8, "metricModelName": "号码数据差异分析", "metricName": "通联账号明细", "compareSystems": "综合搜索,全息档案,案件管理", "description": "某个号码账号特定时间范围内分别进行通话、传真、短信的次数", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1910643125197107201, "metricId": 8, "accountType": "PHONE", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "10", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643125197107202, "metricId": 8, "accountType": "PHONE", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "10", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643125197107203, "metricId": 8, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "10", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643125197107204, "metricId": 8, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "10", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643125197107205, "metricId": 8, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "10", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643125197107206, "metricId": 8, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "10", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643125197107207, "metricId": 8, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "10", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643125197107208, "metricId": 8, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "10", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643125197107209, "metricId": 8, "accountType": "PHONE", "accountValue": "2***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "10", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643125197107210, "metricId": 8, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "10", "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricId", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:01:47.309", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}