{"args": [{"id": 6, "metricModelName": "号码数据差异分析", "metricName": "上网关联虚拟账号", "compareSystems": "综合搜索,全息档案", "description": "某个号码账号特定时间范围内作为认证账号在LIS数据中出现，关联到的虚拟账号", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1910643122881851394, "metricId": 6, "accountType": "PHONE", "accountValue": "***********", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "8", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122881851395, "metricId": 6, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "8", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122881851396, "metricId": 6, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "8", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122881851397, "metricId": 6, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "8", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122881851398, "metricId": 6, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "8", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122881851399, "metricId": 6, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "8", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122881851400, "metricId": 6, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "8", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122881851401, "metricId": 6, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "8", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122881851402, "metricId": 6, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "8", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643122881851403, "metricId": 6, "accountType": "PHONE", "accountValue": "************", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "8", "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricId", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:01:40.284", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}