{"args": [{"id": 2, "metricModelName": "Email数据差异分析", "metricName": "通联账号及次数", "compareSystems": "综合搜索,全息档案,案件管理", "description": "Email账号在特定时间范围内通联的其他Email账号及通联次数", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [{"id": 1910643117295038466, "metricId": 2, "accountType": "EMAIL", "accountValue": "infog@nardi982", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "4", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643117295038467, "metricId": 2, "accountType": "EMAIL", "accountValue": "infog@nardi432", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "4", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643117295038468, "metricId": 2, "accountType": "EMAIL", "accountValue": "infog@nardi6", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "4", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643117295038469, "metricId": 2, "accountType": "EMAIL", "accountValue": "infog@nardi996", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "4", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643117295038470, "metricId": 2, "accountType": "EMAIL", "accountValue": "infog@nardi299", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "4", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643117295038471, "metricId": 2, "accountType": "EMAIL", "accountValue": "infog@nardi485", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "4", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643117295038472, "metricId": 2, "accountType": "EMAIL", "accountValue": "infog@nardi76", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "4", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643117295038473, "metricId": 2, "accountType": "EMAIL", "accountValue": "infog@nardi163", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "4", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643117295038474, "metricId": 2, "accountType": "EMAIL", "accountValue": "infog@nardi189", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "4", "enable": true, "createTime": *************, "modifyTime": *************}, {"id": 1910643117295038475, "metricId": 2, "accountType": "EMAIL", "accountValue": "infog@nardi470", "sampleWeight": 100, "sampleStrategy": "DYNAMIC", "caseParam": null, "dynamic_condition_id": "4", "enable": true, "createTime": *************, "modifyTime": *************}], "method": "getAccountConfigByMetricId", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:01:25.399", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}