{"args": [{"id": 4, "metricModelName": "号码数据差异分析", "metricName": "上网次数", "compareSystems": "综合搜索,全息档案", "description": "某个号码特定时间范围内在LIS数据中作为认证账号出现的次数", "threshold": 0.0005, "checkInterval": "0 0 6 * * ?", "status": "ACTIVE"}, 10000, "2025-04-10,2025-04-10"], "result": [], "method": "getAccountConfigByMetricIdWithStaticAccount", "class": "BusinessAccountConfigServiceImpl", "timestamp": "2025-04-15T21:01:30.021", "argsType": ["com.semptian.entity.BusinessMetricConfig", "java.lang.Integer", "java.lang.String"]}