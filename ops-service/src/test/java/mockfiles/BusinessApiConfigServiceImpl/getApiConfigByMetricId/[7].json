{"args": [7], "result": [{"id": 17, "systemName": "综合搜索", "apiPath": "/senior/query.json?lang=zh_CN", "method": "POST", "paramsTemplate": {"readStatus": "", "dateFormat": "timestamp", "isFieldValAccurate": "", "fieldVal": "{\"resource_table_name\":\"(deye_v64_call OR deye_v64_sms OR deye_v64_fax)\",\"calling_number\":\"#{account}\",\"called_number\":\"#{account}\"}", "pageSize": 20, "tags": "{}", "filterId": 1, "onPage": 1, "aggsField": "", "startTime": 112233, "multiSortField": "{\"_score\":1}", "resultShowType": 1, "endTime": 445566, "fieldList": ["resource_table_name", "AND", "calling_number", "OR", "called_number"], "timeLabel": "0", "containsDetail": true, "resourceTableNames": ["deye_v64_fax", "deye_v64_sms", "deye_v64_call", "deye_v64_voip"], "aggs": ""}, "responseMapping": {"getKey": [], "conditionKey": [], "data.total": 0, "is_kv": 0}, "metricId": 7}, {"id": 18, "systemName": "全息档案", "apiPath": "/common_arc_detail/get_active_trend.json", "method": "GET", "paramsTemplate": {"arcId": "#{arcId}", "dateFormat": "stringDate", "startDay": "#{startDay}", "arcAccount": "#{account}", "endDay": "#{endDay}", "behavior_type": 2, "arcAccountType": 1020004, "lang": "zh_CN", "arcType": 5}, "responseMapping": {"getKey": ["num"], "conditionKey": [], "is_kv": 0, "data.call": 1}, "metricId": 7}, {"id": 19, "systemName": "案件管理", "apiPath": "/clue_hit/page_list.json", "method": "POST", "paramsTemplate": {"noContentFilter": 0, "protocolGroupCode": 200, "dateFormat": "timestamp", "protocolGroupName": "SMS(132)", "clueId": "#{clueId}", "secFilterParam": {}, "isReadNum": 0, "currentTime": 0, "unReadNum": 132, "total": 132, "onPage": 1, "size": 30, "clueName": "********", "protocolCode": "", "caseId": "#{caseId}", "caseName": "yj版本测试1107", "filterNum": 0, "objectName": "call", "startTime": 112233, "endTime": 445566, "objectId": 16828, "similarFilter": 0, "hitTotal": 132}, "responseMapping": {"getKey": [], "conditionKey": [], "data.total": 0, "is_kv": 0}, "metricId": 7}], "method": "getApiConfigByMetricId", "class": "BusinessApiConfigServiceImpl", "timestamp": "2025-04-15T21:01:45.387", "argsType": ["java.lang.Integer"]}