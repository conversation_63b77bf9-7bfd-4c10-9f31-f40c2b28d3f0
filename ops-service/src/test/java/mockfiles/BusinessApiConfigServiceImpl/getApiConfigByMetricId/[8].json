{"args": [8], "result": [{"id": 20, "systemName": "综合搜索", "apiPath": "/senior/query.json?lang=zh_CN", "method": "POST", "paramsTemplate": {"readStatus": "", "dateFormat": "timestamp", "isFieldValAccurate": "{\"calling_number\":true,\"called_number\":true}", "fieldVal": "{\"resource_table_name\":\"(deye_v64_call OR deye_v64_sms OR deye_v64_fax)\",\"calling_number\":\"#{account}\",\"called_number\":\"#{account}\"}", "pageSize": 22, "tags": "{}", "filterId": 1, "onPage": 1, "aggsField": "called_number", "startTime": 112233, "multiSortField": "{\"_score\":1}", "resultShowType": 1, "aggregationField": "called_number", "endTime": 445566, "fieldList": ["resource_table_name", "AND", "calling_number", "OR", "called_number"], "timeLabel": "0", "resultType": 1, "containsDetail": true, "aggQueryType": 3, "resourceTableNames": ["deye_v64_sms", "deye_v64_fax", "deye_v64_call", "deye_v64_voip"], "aggs": ""}, "responseMapping": {"data.fieldInfo.fieldCount.calling_number": 2, "getKey": [], "conditionKey": [], "is_kv": 1, "excludeKey": ["#{account}"]}, "metricId": 8}, {"id": 21, "systemName": "全息档案", "apiPath": "/arc_relation/get_relation_expansion.json", "method": "GET", "paramsTemplate": {"arcId": "#{arcId}", "authAccount": "", "dateFormat": "stringDate", "connectType": "0", "dateOption": "0", "arcType": "5", "expansionRule": "1,2,3", "onPage": "1", "size": "200", "minLinkCount": "1", "startTime": "#{startDay}", "endTime": "#{endDay}", "lang": "zh_CN", "account": "#{account}"}, "responseMapping": {"data.links": 1, "getKey": ["endAccount", "total"], "conditionKey": [], "is_kv": 1}, "metricId": 8}, {"id": 22, "systemName": "案件管理", "apiPath": "/clue_hit/statistics_histogram.json", "method": "GET", "paramsTemplate": {"orderType": 1, "protocolGroupCode": 109, "dateFormat": "timestamp", "mainStartTime": 112233, "clueId": 102427, "onPage": 1, "size": 200, "protocolCode": "", "compareEndTime": 0, "statisticsItem": 5, "lang": "zh_CN", "compareStartTime": 0, "mainEndTime": 445566}, "responseMapping": {"getKey": ["statisticsItem", "mainTimeCount"], "data.list": 1, "conditionKey": [], "is_kv": 1}, "metricId": 8}, {"id": 51, "systemName": "综合搜索", "apiPath": "/senior/query.json?lang=zh_CN", "method": "POST", "paramsTemplate": {"readStatus": "", "dateFormat": "timestamp", "isFieldValAccurate": "{\"calling_number\":true,\"called_number\":true}", "fieldVal": "{\"resource_table_name\":\"(deye_v64_call OR deye_v64_sms OR deye_v64_fax)\",\"calling_number\":\"#{account}\",\"called_number\":\"#{account}\"}", "pageSize": 22, "tags": "{}", "filterId": 1, "onPage": 1, "aggsField": "called_number", "startTime": 112233, "multiSortField": "{\"_score\":1}", "resultShowType": 1, "aggregationField": "called_number", "endTime": 445566, "fieldList": ["resource_table_name", "AND", "calling_number", "OR", "called_number"], "timeLabel": "0", "resultType": 1, "containsDetail": true, "aggQueryType": 3, "resourceTableNames": ["deye_v64_sms", "deye_v64_fax", "deye_v64_call", "deye_v64_voip"], "aggs": ""}, "responseMapping": {"data.fieldInfo.fieldCount.called_number": 2, "getKey": [], "conditionKey": [], "is_kv": 1, "excludeKey": ["#{account}"]}, "metricId": 8}], "method": "getApiConfigByMetricId", "class": "BusinessApiConfigServiceImpl", "timestamp": "2025-04-15T21:01:47.323", "argsType": ["java.lang.Integer"]}