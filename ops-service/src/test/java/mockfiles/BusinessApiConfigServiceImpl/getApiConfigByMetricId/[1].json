{"args": [1], "result": [{"id": 1, "systemName": "综合搜索", "apiPath": "/multi/query.json?lang=zh_CN", "method": "POST", "paramsTemplate": {"readStatus": "", "fileName": "", "dateFormat": "timestamp", "containsAttach": "", "pageSize": 20, "tags": "", "asc": false, "file": null, "onPage": 1, "spamFilter": 0, "isFilterSimilar": false, "secondQueryKeywords": [], "startTime": 112233, "resultShowType": 1, "aggregationField": "", "endTime": 445566, "keyword": "\"#{account}\"", "dimension": [], "timeLabel": "0", "resourceTableNames": ["deye_v64_email"], "aggs": "", "order": "_score", "resourceType": 24}, "responseMapping": {"getKey": [], "conditionKey": [], "data.total": 0, "is_kv": 0}, "metricId": 1}, {"id": 2, "systemName": "全息档案", "apiPath": "/common_arc_detail/get_active_trend.json", "method": "GET", "paramsTemplate": {"arcId": "#{arcId}", "dateFormat": "stringDate", "startDay": "#{startDay}", "arcAccount": "#{account}", "endDay": "#{endDay}", "dataType": "", "behavior_type": "2", "arcAccountType": "", "lang": "zh_CN", "arcType": "2"}, "responseMapping": {"getKey": ["num"], "data.pr": 1, "conditionKey": [], "is_kv": 0}, "metricId": 1}, {"id": 3, "systemName": "案件管理", "apiPath": "/clue_hit/protocol_list.json", "method": "GET", "paramsTemplate": {"lang": "zh_CN", "clueId": "116919"}, "responseMapping": {"getKey": ["hitCount"], "conditionKey": [{"id": 101}], "data.protocolList": 1, "is_kv": 0}, "metricId": 1}], "method": "getApiConfigByMetricId", "class": "BusinessApiConfigServiceImpl", "timestamp": "2025-04-15T21:01:23.179", "argsType": ["java.lang.Integer"]}