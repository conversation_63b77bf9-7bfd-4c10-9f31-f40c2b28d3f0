{
  "result" : {
    "url" : "http://192.168.80.158:8109/archives_web/common_arc_detail/lis/get_distribution.json?lang=zh_CN",
    "method" : "POST",
    "connection" : null,
    "keepAlive" : true
  },
  "executionTime" : 1,
  "method" : "createPost",
  "className" : "cn.hutool.http.HttpUtil",
  "arguments" : [ "\"http://192.168.80.158:8109/archives_web/common_arc_detail/lis/get_distribution.json?lang=zh_CN\"" ],
  "timestamp" : "2025-04-15T21:02:13.238",
  "argsType" : [ "java.lang.String" ]
}
{
  "result" : {
    "url" : "http://192.168.80.158:8109/archives_web/common_arc_detail/lis/get_distribution.json?lang=zh_CN",
    "method" : "POST",
    "connection" : null,
    "keepAlive" : true
  },
  "executionTime" : 0,
  "method" : "createPost",
  "className" : "cn.hutool.http.HttpUtil",
  "arguments" : [ "\"http://192.168.80.158:8109/archives_web/common_arc_detail/lis/get_distribution.json?lang=zh_CN\"" ],
  "timestamp" : "2025-04-15T21:02:13.238",
  "argsType" : [ "java.lang.String" ]
}
