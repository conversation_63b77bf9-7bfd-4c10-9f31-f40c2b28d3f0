{"result": {"url": "http://192.168.80.158:8109/archives_web/im_arc_detail/get_frequent_number_info.json?arcId=25e343fb714ab9d942f5c1889e93c505&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&virtualAppType=1030001&arcAccountType=1030001&virtualAccountAppType=1030001&arcType=6&archiveType=6&size=200&arcAccount=**********&endDay=2025-04-10&lang=zh_CN&virtualAccount=**********", "method": "GET", "connection": null, "keepAlive": true}, "executionTime": 1, "method": "createGet", "className": "cn.hutool.http.HttpUtil", "arguments": ["\"http://192.168.80.158:8109/archives_web/im_arc_detail/get_frequent_number_info.json?arcId=25e343fb714ab9d942f5c1889e93c505&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&virtualAppType=1030001&arcAccountType=1030001&virtualAccountAppType=1030001&arcType=6&archiveType=6&size=200&arcAccount=**********&endDay=2025-04-10&lang=zh_CN&virtualAccount=**********\""], "timestamp": "2025-04-15T21:02:10.778", "argsType": ["java.lang.String"]}