{"result": {"url": "http://192.168.80.158:8109/archives_web/email_arc_detail/get_frequent_email_info.json?arcId=3a6d1eddea0189996e7aca2559cd70a6&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&arcAccountType=&arcType=2&archiveType=2&size=200&arcAccount=<EMAIL>&endDay=2025-04-10&lang=zh_CN&virtualAccount=<EMAIL>", "method": "GET", "connection": null, "keepAlive": true}, "executionTime": 0, "method": "createGet", "className": "cn.hutool.http.HttpUtil", "arguments": ["\"http://192.168.80.158:8109/archives_web/email_arc_detail/get_frequent_email_info.json?arcId=3a6d1eddea0189996e7aca2559cd70a6&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&arcAccountType=&arcType=2&archiveType=2&size=200&arcAccount=bddpvout%40eswil.com&endDay=2025-04-10&lang=zh_CN&virtualAccount=bddpvout%40eswil.com\""], "timestamp": "2025-04-15T21:01:25.877", "argsType": ["java.lang.String"]}