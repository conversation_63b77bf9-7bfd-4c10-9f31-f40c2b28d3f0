{"result": {"url": "http://192.168.80.158:8109/archives_web/common_arc_detail/auth_record.json?arcId=fefaa149f4047c94c6f7d48bd248a3ea&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&arcAccountType=&type=0&arcType=2&archiveType=2&onPage=1&size=200&sortType=&arcAccount=infog@nardi981&endDay=2025-04-10&sortField=&lang=zh_CN&keyword=&virtualAccount=infog@nardi981", "method": "GET", "connection": null, "keepAlive": true}, "executionTime": 0, "method": "createGet", "className": "cn.hutool.http.HttpUtil", "arguments": ["\"http://192.168.80.158:8109/archives_web/common_arc_detail/auth_record.json?arcId=fefaa149f4047c94c6f7d48bd248a3ea&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&arcAccountType=&type=0&arcType=2&archiveType=2&onPage=1&size=200&sortType=&arcAccount=infog%40nardi981&endDay=2025-04-10&sortField=&lang=zh_CN&keyword=&virtualAccount=infog%40nardi981\""], "timestamp": "2025-04-15T21:01:29.120", "argsType": ["java.lang.String"]}