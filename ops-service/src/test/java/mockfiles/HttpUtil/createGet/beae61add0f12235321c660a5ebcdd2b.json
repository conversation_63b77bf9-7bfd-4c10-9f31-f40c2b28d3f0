{"result": {"url": "http://192.168.80.158:8109/archives_web/email_arc_detail/get_frequent_email_info.json?arcId=e09e1207cb8096c7b0f960110b8bf166&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&arcAccountType=&arcType=2&archiveType=2&size=200&arcAccount=infog@nardi996&endDay=2025-04-10&lang=zh_CN&virtualAccount=infog@nardi996", "method": "GET", "connection": null, "keepAlive": true}, "executionTime": 1, "method": "createGet", "className": "cn.hutool.http.HttpUtil", "arguments": ["\"http://192.168.80.158:8109/archives_web/email_arc_detail/get_frequent_email_info.json?arcId=e09e1207cb8096c7b0f960110b8bf166&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&arcAccountType=&arcType=2&archiveType=2&size=200&arcAccount=infog%40nardi996&endDay=2025-04-10&lang=zh_CN&virtualAccount=infog%40nardi996\""], "timestamp": "2025-04-15T21:01:26.316", "argsType": ["java.lang.String"]}