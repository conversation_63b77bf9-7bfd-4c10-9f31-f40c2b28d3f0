{"result": {"url": "http://192.168.80.158:8109/archives_web/arc_relation/get_relation_expansion.json?arcId=c26ab17649ab33e86a9784da20faca2f&authAccount=&dateFormat=stringDate&connectType=0&dateOption=0&arcType=5&expansionRule=1,2,3&onPage=1&size=200&minLinkCount=1&startTime=2025-04-10&endTime=2025-04-10&lang=zh_CN&account=***********", "method": "GET", "connection": null, "keepAlive": true}, "executionTime": 0, "method": "createGet", "className": "cn.hutool.http.HttpUtil", "arguments": ["\"http://192.168.80.158:8109/archives_web/arc_relation/get_relation_expansion.json?arcId=c26ab17649ab33e86a9784da20faca2f&authAccount=&dateFormat=stringDate&connectType=0&dateOption=0&arcType=5&expansionRule=1%2C2%2C3&onPage=1&size=200&minLinkCount=1&startTime=2025-04-10&endTime=2025-04-10&lang=zh_CN&account=***********\""], "timestamp": "2025-04-15T21:01:48.002", "argsType": ["java.lang.String"]}