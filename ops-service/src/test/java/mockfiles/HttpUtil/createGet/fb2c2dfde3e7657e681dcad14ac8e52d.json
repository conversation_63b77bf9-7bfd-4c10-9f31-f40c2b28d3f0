{"result": {"url": "http://192.168.80.158:8109/archives_web/common_arc_detail/get_active_trend.json?arcId=e9782bc6fc187a5f0a2b85ad00179691&dateFormat=stringDate&startDay=2025-04-10&arcAccount=<EMAIL>&endDay=2025-04-10&dataType=&behavior_type=2&arcAccountType=&lang=zh_CN&arcType=2", "method": "GET", "connection": null, "keepAlive": true}, "executionTime": 1, "method": "createGet", "className": "cn.hutool.http.HttpUtil", "arguments": ["\"http://192.168.80.158:8109/archives_web/common_arc_detail/get_active_trend.json?arcId=e9782bc6fc187a5f0a2b85ad00179691&dateFormat=stringDate&startDay=2025-04-10&arcAccount=pqfsbujpo%40seascanner.org&endDay=2025-04-10&dataType=&behavior_type=2&arcAccountType=&lang=zh_CN&arcType=2\""], "timestamp": "2025-04-15T21:01:24.004", "argsType": ["java.lang.String"]}