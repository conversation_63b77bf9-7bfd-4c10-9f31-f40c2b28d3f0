{"result": {"url": "http://192.168.80.158:8109/archives_web/common_arc_detail/auth_record.json?arcId=875476274e1d64ca9e397088085a6ab2&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&virtualAppType=1030001&arcAccountType=1030001&virtualAccountAppType=1030001&type=0&arcType=6&archiveType=6&onPage=1&size=100&sortType=&arcAccount=*********&endDay=2025-04-10&sortField=&lang=zh_CN&keyword=&virtualAccount=*********", "method": "GET", "connection": null, "keepAlive": true}, "executionTime": 0, "method": "createGet", "className": "cn.hutool.http.HttpUtil", "arguments": ["\"http://192.168.80.158:8109/archives_web/common_arc_detail/auth_record.json?arcId=875476274e1d64ca9e397088085a6ab2&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&virtualAppType=1030001&arcAccountType=1030001&virtualAccountAppType=1030001&type=0&arcType=6&archiveType=6&onPage=1&size=100&sortType=&arcAccount=*********&endDay=2025-04-10&sortField=&lang=zh_CN&keyword=&virtualAccount=*********\""], "timestamp": "2025-04-15T21:02:11.567", "argsType": ["java.lang.String"]}