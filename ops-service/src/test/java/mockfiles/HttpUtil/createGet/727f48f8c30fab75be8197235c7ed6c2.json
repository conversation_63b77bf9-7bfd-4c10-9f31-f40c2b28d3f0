{"result": {"url": "http://192.168.80.158:8109/archives_web/phone_arc_detail/get_virtual_account_info.json?arcId=cf59e44b50466bde108919b660394e6a&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&onPage=1&size=500&sortType=&arcAccount=************&endDay=2025-04-10&sortField=&lang=zh_CN&keyword=", "method": "GET", "connection": null, "keepAlive": true}, "executionTime": 0, "method": "createGet", "className": "cn.hutool.http.HttpUtil", "arguments": ["\"http://192.168.80.158:8109/archives_web/phone_arc_detail/get_virtual_account_info.json?arcId=cf59e44b50466bde108919b660394e6a&dateFormat=stringDate&startDay=2025-04-10&dataType=&dateOption=0&onPage=1&size=500&sortType=&arcAccount=************&endDay=2025-04-10&sortField=&lang=zh_CN&keyword=\""], "timestamp": "2025-04-15T21:01:40.906", "argsType": ["java.lang.String"]}