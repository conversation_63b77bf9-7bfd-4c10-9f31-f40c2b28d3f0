package com.semptian.model.metrics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@AllArgsConstructor
@Builder
public class DetailItem {
    private String account;
    private List<SystemData> systems;
    private Double singleDiffRate;

    @Data
    @AllArgsConstructor
    @Builder
    public static class SystemData {
        private Integer appid;
        private String value;
        private Long numericValue;
    }
}

