package com.semptian.model.vo;

import com.semptian.entity.TaskEntity;
import com.semptian.enums.ModuleNameEnum;
import com.semptian.enums.TaskStatusEnum;
import com.semptian.enums.TimeModeEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class TaskVO {
    private Long taskId;
    //统计图表名称
    private String chartName;
    //统计图表描述
    private String chartDesc;
    //统计模块
    private ModuleNameEnum module;
    private TimeModeEnum timeMode;
    private TaskStatusEnum status;
    private String executeRange;
    private Date lastExecTime;
    private String submitUser;

    public static List<TaskVO> convert(List<TaskEntity> records) {
        return records.stream().map(entity -> {
            TaskVO vo = new TaskVO();
            vo.setTaskId(entity.getId());
            vo.setChartName(entity.getChartName());
            vo.setChartDesc(entity.getTaskDesc());
            vo.setModule(entity.getModuleName());
            vo.setTimeMode(entity.getTimeMode());
            vo.setStatus(entity.getStatus());
            vo.setExecuteRange(entity.getExecuteRange());
            vo.setLastExecTime(entity.getLastExecEndtime());
            vo.setSubmitUser(entity.getSubmitUser());
            return vo;
        }).collect(Collectors.toList());
    }
}