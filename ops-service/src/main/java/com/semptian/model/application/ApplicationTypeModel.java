package com.semptian.model.application;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/9/1
 * @Description
 **/
@Data
@ApiModel
public class ApplicationTypeModel {

    @ApiModelProperty(value = "应用分类id,当有应用id的时候为编辑，无id时为添加", example = "3221312131")
    private String id;

    @ApiModelProperty(value = "应用分类名称",example = "appType")
    private String typeName;

    @ApiModelProperty(value = "父id,为空表示顶层")
    private Integer parentId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "排序")
    private Integer order;

    @ApiModelProperty(value = "当前用户id")
    private String curUser;
}
