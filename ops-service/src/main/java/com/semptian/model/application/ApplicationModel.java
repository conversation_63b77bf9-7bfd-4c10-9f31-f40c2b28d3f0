package com.semptian.model.application;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/2/28 14:18
 */
@Data
@ApiModel
public class ApplicationModel {

    @ApiModelProperty(value = "应用id,当有应用id的时候为编辑，无id时为添加", example = "3221312131")
    private Integer id;

    @ApiModelProperty(value = "应用名称", example = "app1")
    private String appName;

    @ApiModelProperty(value = "应用分类id")
    private String parentId;

    @ApiModelProperty(value = "应用url", example = "http://32.32.3.3:8080/")
    private String appUrl;

    @ApiModelProperty(value = "应用版本号", example = "1.0")
    private String appVersion;

    @ApiModelProperty(value = "应用备注", example = "13243242")
    private String appDesc;

    @ApiModelProperty(value = "当前登录用户")
    private String curUser;

    private String changeName;
}
