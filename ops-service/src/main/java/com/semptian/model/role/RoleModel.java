package com.semptian.model.role;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/9/10
 * @Description
 **/
@Data
@ApiModel
public class RoleModel {

    @ApiModelProperty(value = "角色id,当有角色id的时候为编辑，无id时为添加")
    private String id;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "角色描述")
    private String roleDesc;

    @ApiModelProperty(value = "所属职位id")
    private Integer postId;

    @ApiModelProperty(value = "绑定组织id")
    private Integer orgId;

    @ApiModelProperty(value = "绑定用户组id")
    private Integer groupId;

    @ApiModelProperty(value = "当前用户id")
    private String curUser;

    @ApiModelProperty(value = "角色对应的应用权限id,多个id之间用逗号分隔")
    private String appIds;

    @ApiModelProperty(value = "角色对应的菜单权限id,多个id之间用逗号分隔")
    private String menuIds;

    @ApiModelProperty(value = "角色对应的操作权限,多个操作权限id之间用逗号分隔")
    private String operateIds;

    private String changeName;
}
