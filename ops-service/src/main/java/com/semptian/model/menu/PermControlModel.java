package com.semptian.model.menu;


/**
 * <AUTHOR>
 * @date 2020/06/16
 * 获取用户菜单模型
 */
public class PermControlModel {

    /**
     * id
     */
    private String id;

    /**
     * 父ID
     */
    private String parentId;

    /**
     * 应用ID
     */
    private int appId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 权限菜单名称
     */
    private String permName;

    /**
     * 权限菜单URL
     */
    private String permUrl;

    /**
     * 权限菜单图标
     */
    private String permIcon;

    /**
     * 创建时间
     */
    private long createTime;

    /**
     * 修改时间
     */
    private long modifyTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public int getAppId() {
        return appId;
    }

    public void setAppId(int appId) {
        this.appId = appId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getPermName() {
        return permName;
    }

    public void setPermName(String permName) {
        this.permName = permName;
    }

    public String getPermUrl() {
        return permUrl;
    }

    public void setPermUrl(String permUrl) {
        this.permUrl = permUrl;
    }

    public String getPermIcon() {
        return permIcon;
    }

    public void setPermIcon(String permIcon) {
        this.permIcon = permIcon;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(long modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "PermControlEntity{" +
                "id='" + id + '\'' +
                ", parentId='" + parentId + '\'' +
                ", appId='" + appId + '\'' +
                ", appName='" + appName + '\'' +
                ", permName='" + permName + '\'' +
                ", permUrl='" + permUrl + '\'' +
                ", permIcon='" + permIcon + '\'' +
                ", createTime=" + createTime +
                ", modifyTime=" + modifyTime +
                '}';
    }
}
