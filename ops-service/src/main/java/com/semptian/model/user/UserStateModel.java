package com.semptian.model.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户更新状态模型
 *
 * <AUTHOR>
 * @date 2020/2/26 17:40
 */
@ApiModel(value = "用户更新状态模型")
@Data
public class UserStateModel {

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "用户name")
    private String userName;

    @ApiModelProperty(value = "用户状态 1.可用 2.停用 0.注销", example = "1")
    private Integer userState;
}
