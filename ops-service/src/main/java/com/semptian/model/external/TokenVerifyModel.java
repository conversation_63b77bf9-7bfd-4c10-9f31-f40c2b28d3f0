package com.semptian.model.external;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * token验证模型
 *
 * <AUTHOR>
 * @date 2020/2/26 17:40
 */
@ApiModel(value = "token验证模型")
@Data
public class TokenVerifyModel {

    @ApiModelProperty(value = "token", example = "fsadfdffafsdf")
    private String token;

    @ApiModelProperty(value = "客户端ip，可不填写", example = "*******")
    private String clientIp;

    @ApiModelProperty(value = "appId", example = "70")
    private String appId;

}
