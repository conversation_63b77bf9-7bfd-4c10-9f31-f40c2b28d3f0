package com.semptian.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.semptian.enums.TimeModeEnum;
import com.semptian.model.dto.TaskQueryDTO;
import com.semptian.model.vo.TaskVO;

public interface TaskService {
    /**
     * 分页查询任务
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<TaskVO> getTasksByPage(TaskQueryDTO queryDTO);


    /**
     * 执行任务
     *
     * @param taskId       任务ID
     * @param timeMode
     * @param executeRange
     * @return 执行结果
     */
    Boolean executeTasks(String taskId, TimeModeEnum timeMode, String executeRange);
}