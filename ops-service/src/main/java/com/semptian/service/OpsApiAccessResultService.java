package com.semptian.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.semptian.entity.OpsApiAccessResult;

import java.time.LocalDate;
import java.util.List;

public interface OpsApiAccessResultService extends IService<OpsApiAccessResult> {
    Double calculateDiffRate(Long id, LocalDate startDate, LocalDate endDate);

    Page<OpsApiAccessResult> pageQuery(Long aLong, Page<Object> objectPage, String account, String sort, boolean desc, LocalDate startDate, LocalDate endDate);

    Double calculateAccountDiffRate(Long aLong, String opsApiAccessResults, LocalDate startDate, LocalDate endDate, List<OpsApiAccessResult> accountResults);
}