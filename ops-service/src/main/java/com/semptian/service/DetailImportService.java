package com.semptian.service;

import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;

/**
 * 明细导入服务接口
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
public interface DetailImportService {

    /**
     * 导入CSV文件到Doris
     *
     * @param metricId   指标ID，可为空，为空时导入所有找到的CSV文件
     * @param targetDate 目标日期，指定要导入哪个日期的CSV文件
     * @param tableName  目标Doris表名，不传时使用默认表名
     * @return 导入结果信息
     */
    String importCsvToDoris(Integer metricId, LocalDate targetDate, String tableName);

    /**
     * 上传CSV文件并导入到Doris
     *
     * @param file      CSV文件
     * @param metricId  指标ID
     * @param tableName 目标Doris表名，不传时使用默认表名
     * @return 导入结果信息
     */
    String uploadAndImportCsv(MultipartFile file, Integer metricId, String tableName);

    /**
     * 查询导入任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    Object getImportTaskStatus(String taskId);

    /**
     * 导入CSV文件到ops_api_access_result表
     *
     * @param file CSV文件
     * @return 导入结果信息
     */
    String importOpsApiAccessResultCsv(MultipartFile file);
}
