package com.semptian.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.annotation.OperateMock;
import com.semptian.entity.BusinessSysConfig;
import com.semptian.mapper.BusinessSysConfigMapper;
import com.semptian.service.BusinessSysConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Vector;

@Service
@Transactional
public class BusinessSysConfigServiceImpl extends ServiceImpl<BusinessSysConfigMapper, BusinessSysConfig> implements BusinessSysConfigService {

    @Override
    public Page<BusinessSysConfig> getSysConfigList(int pageNum, int pageSize, String sysName, String sysType, Boolean enable) {
        Page<BusinessSysConfig> page = new Page<>(pageNum, pageSize);
        QueryWrapper<BusinessSysConfig> wrapper = new QueryWrapper<>();
        if (sysName != null) wrapper.like("sys_name", sysName);
        if (sysType != null) wrapper.eq("sys_type", sysType);
        if (enable != null) wrapper.eq("enable", enable);
        return baseMapper.selectPage(page, wrapper);
    }

    @Override
    public BusinessSysConfig getSysConfigById(Long id) {
        if (id != null) {
            return baseMapper.selectById(id);
        }
        return null;
    }

    @OperateMock
    @Override
    public List<BusinessSysConfig> getSysConfigBySysNameAndMetricId(String[] compareSystems, Integer metricId) {
        List<BusinessSysConfig> sysConfigs = new Vector<>();
        for (String compareSystem : compareSystems) {
            QueryWrapper<BusinessSysConfig> wrapper = new QueryWrapper<>();
            wrapper.eq("sys_name", compareSystem);
            BusinessSysConfig sysConfig = baseMapper.selectOne(wrapper);
            if (sysConfig != null) {
                sysConfigs.add(sysConfig);
            }
        }
        return sysConfigs;
    }
}