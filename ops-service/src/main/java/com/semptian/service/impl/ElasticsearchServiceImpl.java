package com.semptian.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.semptian.service.ElasticsearchService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.util.EntityUtils;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.elasticsearch.client.RestClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;

/**
 * Elasticsearch服务实现类
 */
@Slf4j
@Service
public class ElasticsearchServiceImpl implements ElasticsearchService {

    @Value("${elasticsearch.host:localhost}")
    private String host;

    @Value("${elasticsearch.port:9200}")
    private int port;

    @Value("${elasticsearch.timeout:30}")
    private int timeout;

    private RestHighLevelClient client;

    @Value("${elasticsearch.disable_warnings:true}")
    private boolean disableWarnings;

    @PostConstruct
    public void init() {
        // 创建RestClientBuilder
        RestClientBuilder builder = RestClient.builder(
                new HttpHost(host, port, "http")
        );

        // 禁用安全警告
        if (disableWarnings) {
            // 添加请求头来禁用安全警告
            builder.setDefaultHeaders(new Header[]{
                new BasicHeader("X-Elastic-Product", "Elasticsearch")
            });
            log.info("[Elasticsearch] 已禁用安全警告提示");
        }

        client = new RestHighLevelClient(builder);
        log.info("[Elasticsearch] 客户端初始化成功 | 地址: {}:{}", host, port);
    }

    @PreDestroy
    public void close() {
        if (client != null) {
            try {
                client.close();
                log.info("[Elasticsearch] 客户端关闭成功");
            } catch (IOException e) {
                log.error("[Elasticsearch] 客户端关闭失败", e);
            }
        }
    }

    @Override
    public JSONObject executeDslQuery(String indices, String dsl) {
        try {
            // 使用低级别的REST客户端直接发送DSL查询
            // 这种方式绕过了Elasticsearch Java API的解析过程

            // 获取低级别客户端
            RestClient lowLevelClient = client.getLowLevelClient();

            // 准备请求
            Request request = new Request(
                    "GET",
                    "/" + indices + "/_search"
            );

            // 设置请求参数
            request.addParameter("timeout", timeout + "s");

            // 设置请求体
            request.setJsonEntity(dsl);

            // 尝试格式化DSL以便于阅读
            String formattedDsl = formatJsonIfPossible(dsl);
            //log.info("[Elasticsearch] 执行查询 | 索引: {} | DSL: \n{}\n", indices, formattedDsl);

            // 执行请求
            Response esResponse = lowLevelClient.performRequest(request);

            // 解析响应
            String responseBody = EntityUtils.toString(esResponse.getEntity());
            // 尝试格式化响应以便于阅读
            String formattedResponse = formatJsonIfPossible(responseBody);
            log.debug("[Elasticsearch] 查询响应: \n{}\n", formattedResponse);

            // 将响应转换为JSONObject
            return JSON.parseObject(responseBody);
        } catch (IOException e) {
            log.error("[Elasticsearch] 查询错误", e);
            throw new RuntimeException("[Elasticsearch] 查询错误: " + e.getMessage(), e);
        }
    }

    /**
     * 尝试格式化JSON字符串，如果是有效的JSON则返回格式化后的字符串，否则返回原始字符串
     *
     * @param jsonStr 要格式化的JSON字符串
     * @return 格式化后的字符串
     */
    private String formatJsonIfPossible(String jsonStr) {
        try {
            // 尝试解析JSON
            Object jsonObj = JSON.parse(jsonStr);
            // 如果解析成功，返回格式化后的字符串
            return JSON.toJSONString(jsonObj, true);
        } catch (Exception e) {
            // 如果解析失败，返回原始字符串
            return jsonStr;
        }
    }
}
