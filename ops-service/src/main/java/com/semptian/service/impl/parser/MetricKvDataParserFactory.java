package com.semptian.service.impl.parser;

import com.semptian.service.MetricKvDataParser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 指标kv数据解析工厂
 *
 * <AUTHOR> Hu
 * @since 2025/5/21
 */
@Service
public class MetricKvDataParserFactory {

    private final List<MetricKvDataParser> parsers;

    @Autowired
    public MetricKvDataParserFactory(List<MetricKvDataParser> parsers) {
        this.parsers = parsers;
    }

    public MetricKvDataParser getParser(Integer metricId, String systemName) {
        for (MetricKvDataParser parser : parsers) {
            if (parser.getMetricIds().contains(metricId) && parser.getSystemNames().contains(systemName)) {
                return parser;
            }
        }

        //找不到则使用默认解析器
        return parsers.stream().filter(parser -> parser.getClass().equals(MetricKvDataDefaultParser.class)).findFirst()
                .orElseThrow(() -> new IllegalArgumentException("No parser found for metricId: " + metricId));
    }
}
