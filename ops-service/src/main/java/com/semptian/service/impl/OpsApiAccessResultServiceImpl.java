package com.semptian.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.entity.OpsApiAccessResult;
import com.semptian.mapper.OpsApiAccessResultMapper;
import com.semptian.service.OpsApiAccessResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Service
@DS("doris")
public class OpsApiAccessResultServiceImpl extends ServiceImpl<OpsApiAccessResultMapper, OpsApiAccessResult> implements OpsApiAccessResultService {
    @Resource
    private OpsApiAccessResultMapper opsApiAccessResultMapper;

    //计算所有账号系统间一致率
    @Override
    public Double calculateDiffRate(Long id, LocalDate startDate, LocalDate endDate) {
        List<OpsApiAccessResult> opsApiAccessResults = opsApiAccessResultMapper.selectList(new QueryWrapper<OpsApiAccessResult>().eq("metric_id", id).between("stat_date", startDate.toString(), endDate.toString()));
        long totalAccounts = opsApiAccessResults.stream().map(OpsApiAccessResult::getAccount).distinct().count();
        if (totalAccounts == 0) return 0.0;
        long consistentAccounts = opsApiAccessResults.stream().collect(Collectors.groupingBy(OpsApiAccessResult::getAccount)).values().stream().filter(accountList -> {
            // 如果对比的系统只有一个，则返回差异率为1
            if (accountList.size() == 1) {
                return false;
            }
            // 检查账号在所有统计日期的系统数据是否完全一致
            return accountList.stream().collect(Collectors.groupingBy(OpsApiAccessResult::getStatDate)).values().stream().allMatch(dateList -> {
                double firstStat = dateList.get(0).getStatCount();
                return dateList.stream().allMatch(r -> r.getStatCount() == firstStat);
            });
        }).count();
        return 1 - ((double) consistentAccounts / totalAccounts);
    }

    //计算指定账号的系统间一致率
    @Override
    public Double calculateAccountDiffRate(Long id, String account, LocalDate startDate, LocalDate endDate, List<OpsApiAccessResult> accountResults) {
        AtomicReference<Double> diffRateSum = new AtomicReference<>(0.0);
        AtomicInteger validDataCount = new AtomicInteger();
        accountResults.stream().collect(Collectors.groupingBy(OpsApiAccessResult::getStatDate)).forEach((statDate, dateList) -> {
            //如果对比的系统只有一个，则返回差异率为1
            if (dateList.size() == 1) {
                diffRateSum.updateAndGet(v -> v + 100);
                validDataCount.incrementAndGet();
                return;
            }
            if (dateList.size() >= 2) {
                double maxStat = dateList.stream().mapToDouble(OpsApiAccessResult::getStatCount).max().orElse(0);
                double minStat = dateList.stream().mapToDouble(OpsApiAccessResult::getStatCount).min().orElse(0);

                if (maxStat != 0) {
                    double diffRate = 100 * (maxStat - minStat) / maxStat;
                    diffRateSum.updateAndGet(v -> v + diffRate);
                    validDataCount.incrementAndGet();
                }
            }
        });
        return validDataCount.get() > 0 ? diffRateSum.get() / validDataCount.get() : 0.0;
    }

    @Override
    public Page<OpsApiAccessResult> pageQuery(Long aLong, Page<Object> objectPage, String account, String sort, boolean desc, LocalDate startDate, LocalDate endDate) {
        QueryWrapper<OpsApiAccessResult> queryWrapper = new QueryWrapper<>();
        if (aLong != null) {
            queryWrapper.eq("metric_id", aLong);
        }
        if (account != null && !account.isEmpty()) {
            queryWrapper.eq("account", account);
        }
        if (startDate != null && endDate != null) {
            queryWrapper.between("stat_date", startDate.toString(), endDate.toString());
        }
        if (sort != null && !sort.isEmpty()) {
            if (desc) {
                queryWrapper.orderByDesc(sort);
            } else {
                queryWrapper.orderByAsc(sort);
            }
        }
        Page<OpsApiAccessResult> page = new Page<>(objectPage.getCurrent(), objectPage.getSize());
        return opsApiAccessResultMapper.selectPage(page, queryWrapper);
    }
}
