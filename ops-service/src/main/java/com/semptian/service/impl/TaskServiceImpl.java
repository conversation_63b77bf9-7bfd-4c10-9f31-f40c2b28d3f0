package com.semptian.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.semptian.entity.TaskEntity;
import com.semptian.enums.TimeModeEnum;
import com.semptian.mapper.TaskMapper;
import com.semptian.model.dto.TaskQueryDTO;
import com.semptian.model.vo.TaskVO;
import com.semptian.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;

@Slf4j
@Service
public class TaskServiceImpl implements TaskService {
    @Resource
    private TaskMapper taskMapper;


    @Override
    public Page<TaskVO> getTasksByPage(@Valid TaskQueryDTO queryDTO) {
        log.debug("查询任务列表，参数：{}", queryDTO);
        Page<TaskEntity> page = new Page<>(queryDTO.getPage(), queryDTO.getSize());
        IPage<TaskEntity> entityPage = taskMapper.selectTaskPage(page, queryDTO.getModule(), queryDTO.getTimeMode(), queryDTO.getStatus(), queryDTO.getChartName());
        // 转换为VO
        Page<TaskVO> taskVOPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal(), entityPage.isSearchCount());
        taskVOPage.setRecords(TaskVO.convert(entityPage.getRecords()));
        return taskVOPage;
    }

    @Override
    public Boolean executeTasks(String taskId, TimeModeEnum timeMode, String executeRange) {
        long startTime = System.currentTimeMillis();
        try {
            if (StringUtils.isBlank(taskId)) {
                throw new IllegalArgumentException("任务ID无效");
            }

            // 更新任务状态为执行中
            taskMapper.updateStatus(taskId, "EXECUTING");
            log.debug("任务[{}]状态更新为执行中", taskId);

            // 模拟任务执行（实际业务逻辑）
            Thread.sleep(2000);

            // 更新任务状态为已完成
            taskMapper.updateStatus(taskId, "COMPLETED");
            log.debug("任务[{}]执行完成，耗时：{}ms", taskId, System.currentTimeMillis() - startTime);

            //TODO  执行回调通知
            //callbackService.notifyCompletion(taskId);
            return true;
        } catch (InterruptedException e) {
            taskMapper.updateStatus(taskId, "FAILED");
            log.error("任务[{}]执行被中断：{}", taskId, e.getMessage());
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            taskMapper.updateStatus(taskId, "FAILED");
            log.error("任务[{}]执行失败：{}", taskId, e.getMessage());
            throw new RuntimeException("任务执行异常", e);
        }
        return null;
    }
}