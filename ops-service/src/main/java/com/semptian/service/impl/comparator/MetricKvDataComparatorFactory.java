package com.semptian.service.impl.comparator;

import com.semptian.service.MetricKvDataComparator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 指标kv数据对比工厂
 *
 * <AUTHOR> Hu
 * @since 2025/5/22
 */
@Service
public class MetricKvDataComparatorFactory {

    private final List<MetricKvDataComparator> comparators;

    @Autowired
    public MetricKvDataComparatorFactory(List<MetricKvDataComparator> comparators) {
        this.comparators = comparators;
    }

    public MetricKvDataComparator getComparator(Integer metricId) {
        for (MetricKvDataComparator comparator : comparators) {
            if (comparator.getMetricIds().contains(metricId)) {
                return comparator;
            }
        }

        //找不到则使用默认解析器
        return comparators.stream().filter(comparator -> comparator.getClass().equals(MetricKvDataDefaultComparator.class)).findFirst()
                .orElseThrow(() -> new IllegalArgumentException("No comparator found for metricId: " + metricId));
    }
}