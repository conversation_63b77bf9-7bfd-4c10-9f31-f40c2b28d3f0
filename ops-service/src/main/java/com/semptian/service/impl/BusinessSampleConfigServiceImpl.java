package com.semptian.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.annotation.OperateMock;
import com.semptian.base.template.TianHeDorisTemplate;
import com.semptian.entity.BusinessSampleConfig;
import com.semptian.mapper.BusinessSampleConfigMapper;
import com.semptian.service.BusinessSampleConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BusinessSampleConfigServiceImpl extends ServiceImpl<BusinessSampleConfigMapper, BusinessSampleConfig> implements BusinessSampleConfigService {

    @Resource
    private BusinessSampleConfigMapper configMapper;


    @OperateMock
    @Override
    public List<BusinessSampleConfig> getValidConfigsByMetricId(Long metricId) {
        QueryWrapper<BusinessSampleConfig> wrapper = new QueryWrapper<>();
        wrapper.eq("metric_id", metricId).eq("enable", 1);
        return configMapper.selectList(wrapper);
    }

    @OperateMock
    @Override
    public List<String> executeDynamicSampling(String dynamicSql) {
        log.info("开始执行动态采样SQL: \n{}\n", dynamicSql);
        try {
            long startTime = System.currentTimeMillis();
            List<Map<String, Object>> results = configMapper.executeDynamicSQL(dynamicSql);
            long endTime = System.currentTimeMillis();

            List<String> accounts = results.stream()
                    .map(obj -> obj.get("account").toString())
                    .collect(Collectors.toList());

            log.info("动态采样SQL执行完成，耗时: {}ms, 获取账号数量: {}", (endTime - startTime), accounts.size());
            if (!accounts.isEmpty()) {
                log.info("采样账号示例(最多5个): {}", accounts.stream().limit(5).collect(Collectors.joining(", ")));
            }

            return accounts;
        } catch (Exception e) {
            log.error("执行动态SQL失败: {}, 错误信息: {}", dynamicSql, e.getMessage(), e);
            return null;
        }
    }
}