package com.semptian.service.impl.comparator;

import com.semptian.service.MetricKvDataComparator;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 指标kv数据默认对比器
 *
 * <AUTHOR>
 * @since 2025/5/22
 */

@Service
public class MetricKvDataDefaultComparator implements MetricKvDataComparator {

    @Override
    public List<String> getDiffData(Map<String, Map<String, Integer>> systemKvMap) {
        // 存储差异数据
        List<String> diffData = new ArrayList<>();

        if (systemKvMap.size() > 1) {
            // 收集所有存在的key
            Set<String> allKeys = new HashSet<>();
            for (Map<String, Integer> kvMap : systemKvMap.values()) {
                allKeys.addAll(kvMap.keySet());
            }

            // 遍历所有key进行对比
            for (String key : allKeys) {
                Map<String, Object> keyValueMap = new HashMap<>();
                for (Map.Entry<String, Map<String, Integer>> systemEntry : systemKvMap.entrySet()) {
                    String systemName = systemEntry.getKey();
                    Map<String, Integer> kvMap = systemEntry.getValue();
                    keyValueMap.put(systemName, null == kvMap.get(key) ? 0 : kvMap.get(key));
                }

                // 检查值是否一致
                Set<Object> valueSet = keyValueMap.values().stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                if (valueSet.size() > 1) {
                    diffData.add(key);
                }
            }
        }else if (systemKvMap.size() == 1) {
            // 如果只有一个系统，则返回所有的key
            return new ArrayList<>(systemKvMap.values().iterator().next().keySet());
        }
        return diffData;
    }

    @Override
    public List<Integer> getMetricIds() {
        return Collections.emptyList();
    }
}