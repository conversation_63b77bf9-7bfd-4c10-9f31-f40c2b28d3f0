package com.semptian.service.impl;

import com.semptian.entity.DataEaseLink;
import com.semptian.mapper.DataEaseLinkMapper;
import com.semptian.service.DataEaseLinkService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class DataEaseLinkServiceImpl implements DataEaseLinkService {

    @Resource
    private DataEaseLinkMapper dataEaseLinkMapper;

    @Override
    public List<Object> getLinks(String moduleType) {
        List<DataEaseLink> links = dataEaseLinkMapper.selectLinks(moduleType);

        return links.stream().map(link -> new Object() {
            public final String id = link.getId();
            public final String name = link.getDashboardName();
            public final String url = link.getEmbedUrl();
            public final String module = link.getModuleType();
        }).collect(Collectors.toList());
    }
}