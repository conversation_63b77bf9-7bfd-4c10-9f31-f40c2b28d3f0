package com.semptian.service.impl;

import com.google.common.collect.Lists;
import com.semptian.base.service.ReturnModel;
import com.semptian.common.CommonReturnCode;
import com.semptian.entity.UserEntity;
import com.semptian.mapper.UserMapper;
import com.semptian.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;
    @Override
    public Object queryUserInfoById(Long userId, Integer isBasicInfo) {
        UserEntity userEntity = userMapper.selectById(userId);
        if (userEntity == null) {
            return new ReturnModel(CommonReturnCode.USER_NOT_EXIST.getCode(), CommonReturnCode.USER_NOT_EXIST.getMsg());
        }

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("id", userEntity.getId());
        resultMap.put("userName", userEntity.getUserName());
        resultMap.put("account", userEntity.getAccount());
        resultMap.put("userState", 1);
        resultMap.put("isAdmin", 1);
        resultMap.put("userImg", "http://192.168.80.158:9000/view/2020/11/14/*************.png");
        resultMap.put("loginNum", 381);
        resultMap.put("lastOrgId", "-1");
        resultMap.put("gender", 1);
        resultMap.put("create_time", userEntity.getCreateTime());
        resultMap.put("expiration", 0);
        resultMap.put("heartbeatInterval", 30000);
        resultMap.put("positionIds", Collections.singletonList(0));
        resultMap.put("isSimple", true);

        if (isBasicInfo == 1) {
            return new ReturnModel<>().ok(resultMap);
        } else {
            // 添加额外的信息
            resultMap.put("finger", new ArrayList<>());
            resultMap.put("portalMenuList", Lists.newArrayList());
            resultMap.put("portalOperateList", Lists.newArrayList());
            resultMap.put("postIds", Collections.singletonList(0));
            resultMap.put("orgs", new ArrayList<>());
            resultMap.put("userRoleId", Collections.singletonList("1"));
            resultMap.put("userRole", Collections.singletonList("SuperAdmin"));
            resultMap.put("manageOrgs", new ArrayList<>());

            return new ReturnModel<>().ok(resultMap);
        }
    }

    @Override
    public UserEntity selectByAccount(String account) {
        return userMapper.selectByAccount(account);
    }
}