package com.semptian.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.entity.OpsApiAccessResultDifference;
import com.semptian.mapper.OpsApiAccessResultDifferenceMapper;
import com.semptian.service.OpsApiAccessResultDifferenceService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 系统间一致性统计结果差异
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
@Service
@DS("doris")
public class OpsApiAccessResultDifferenceServiceImpl extends ServiceImpl<OpsApiAccessResultDifferenceMapper, OpsApiAccessResultDifference> implements OpsApiAccessResultDifferenceService {

    @Override
    public void deleteByMetricIdAndTimeRange(Integer metricId, LocalDate startDate, LocalDate endDate, String account) {
        baseMapper.deleteByMetricIdAndTimeRange(metricId, startDate, endDate, account);
    }

    @Override
    public List<OpsApiAccessResultDifference> selectDifferenceAccountsBySql(Integer metricId, Date startDate, Date endDate) {
        return baseMapper.selectDifferenceAccountsBySql(metricId, startDate, endDate);
    }
}
