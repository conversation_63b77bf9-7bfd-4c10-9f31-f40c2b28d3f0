package com.semptian.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.semptian.component.DorisStreamLoadUtil;
import com.semptian.component.RedisLockUtil;
import com.semptian.entity.DetailFieldConfig;
import com.semptian.entity.DetailImportTask;
import com.semptian.entity.OpsApiAccessResult;
import com.semptian.mapper.DetailFieldConfigMapper;
import com.semptian.mapper.DetailImportTaskMapper;
import com.semptian.mapper.OpsApiAccessResultMapper;
import com.semptian.service.DetailImportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;


import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 明细导入服务实现类
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Slf4j
@Service
public class DetailImportServiceImpl implements DetailImportService {

    @Resource
    private DetailImportTaskMapper detailImportTaskMapper;

    @Resource
    private DetailFieldConfigMapper detailFieldConfigMapper;

    @Resource
    private DorisStreamLoadUtil dorisStreamLoadUtil;

    @Resource
    private RedisLockUtil redisLockUtil;

    @Resource
    private DorisColumnMappingUtil dorisColumnMappingUtil;

    @Resource
    private OpsApiAccessResultMapper opsApiAccessResultMapper;

    @Value("${detail.export.base.path:/data/detail_export}")
    private String basePath;

    @Value("${detail.import.default.table.prefix:ops_detail_}")
    private String defaultTablePrefix;

    @Value("${detail.import.batch.size:1000}")
    private int batchSize;

    @Value("${detail.import.stream.batch.size:500}")
    private int streamBatchSize;

    @Value("${detail.import.temp.path:/tmp/detail_import}")
    private String tempPath;

    @Value("${detail.import.max.file.size:1073741824}")
    private long maxFileSize;

    // ops_api_access_result表名
    private static final String OPS_API_ACCESS_RESULT_TABLE = "ops_api_access_result";

    // 异步处理线程池
    private final ExecutorService asyncExecutor = Executors.newCachedThreadPool();

    @Override
    public String importCsvToDoris(Integer metricId, LocalDate targetDate, String tableName) {
        // 如果metricId为空，则导入所有找到的CSV文件
        if (metricId == null) {
            return importAllCsvFiles(targetDate, tableName);
        } else {
            return importSingleMetricCsv(metricId, targetDate, tableName);
        }
    }

    @Override
    public String uploadAndImportCsv(MultipartFile file, Integer metricId, String tableName) {
        String taskId = IdUtil.simpleUUID();
        String lockKey = "detail_import_upload_" + metricId + "_" + taskId;

        // 获取分布式锁
        if (!redisLockUtil.tryLockByValidityTime(lockKey)) {
            String message = String.format("指标[%d]的上传导入任务正在执行中", metricId);
            log.warn(message);
            return message;
        }

        try {
            // 检查文件大小
            if (file.getSize() > maxFileSize) {
                String message = String.format("文件大小超过限制 | 当前大小: %d 字节 (%.2f MB) | 最大允许: %d 字节 (%.2f MB)",
                        file.getSize(), file.getSize() / 1024.0 / 1024.0,
                        maxFileSize, maxFileSize / 1024.0 / 1024.0);
                log.warn("[明细导入] {}", message);
                return message;
            }

            // 创建导入任务记录
            DetailImportTask task = createImportTask(taskId, metricId, null, tableName,
                    file.getOriginalFilename(), file.getSize());

            // 先保存上传文件到临时目录（在异步处理之前）
            Path tempFile = saveUploadedFile(file, taskId);

            // 更新任务文件路径
            task.setFilePath(tempFile.toString());
            detailImportTaskMapper.updateById(task);

            // 异步处理上传文件导入
            CompletableFuture.runAsync(() -> {
                try {
                    processUploadFileImportFromPath(task, tempFile);
                } catch (Exception e) {
                    log.error("[明细导入] 上传文件导入异步处理失败 | 任务ID: {} | 错误: {}", taskId, e.getMessage(), e);
                    updateTaskStatus(taskId, DetailImportTask.Status.FAILED, e.getMessage());
                }
            }, asyncExecutor);

            return String.format("上传导入任务已提交，任务ID: %s", taskId);

        } catch (Exception e) {
            log.error("[明细导入] 上传文件保存失败 | 任务ID: {} | 错误: {}", taskId, e.getMessage(), e);
            updateTaskStatus(taskId, DetailImportTask.Status.FAILED, e.getMessage());
            return String.format("上传导入任务失败: %s", e.getMessage());
        } finally {
            redisLockUtil.unlock(lockKey);
        }
    }

    @Override
    public Object getImportTaskStatus(String taskId) {
        DetailImportTask task = detailImportTaskMapper.getByTaskId(taskId);
        if (task == null) {
            throw new RuntimeException("未找到任务ID: " + taskId);
        }

        Map<String, Object> status = new HashMap<>();
        status.put("taskId", task.getTaskId());
        status.put("metricId", task.getMetricId());
        status.put("targetDate", task.getTargetDate());
        status.put("tableName", task.getTableName());
        status.put("fileName", task.getFileName());
        status.put("fileSize", task.getFileSize());
        status.put("totalRows", task.getTotalRows());
        status.put("processedRows", task.getProcessedRows());
        status.put("successRows", task.getSuccessRows());
        status.put("failedRows", task.getFailedRows());
        status.put("status", task.getStatus());
        status.put("statusDescription", DetailImportTask.Status.fromCode(task.getStatus()).getDescription());
        status.put("errorMessage", task.getErrorMessage());
        status.put("startTime", task.getStartTime());
        status.put("endTime", task.getEndTime());
        status.put("createTime", task.getCreateTime());

        // 计算进度百分比
        if (task.getTotalRows() != null && task.getTotalRows() > 0) {
            double progress = (double) (task.getProcessedRows() != null ? task.getProcessedRows() : 0) / task.getTotalRows() * 100;
            status.put("progress", Math.round(progress * 100.0) / 100.0);
        } else {
            status.put("progress", 0.0);
        }

        return status;
    }

    /**
     * 导入所有找到的CSV文件
     */
    private String importAllCsvFiles(LocalDate targetDate, String tableName) {
        String lockKey = "detail_import_all_" + targetDate;

        // 获取分布式锁
        if (!redisLockUtil.tryLockByValidityTime(lockKey)) {
            String message = String.format("日期[%s]的全量导入任务正在执行中", targetDate);
            log.warn(message);
            return message;
        }

        try {
            // 扫描目标日期的所有CSV文件
            List<Path> csvFiles = scanCsvFiles(targetDate, null);
            if (csvFiles.isEmpty()) {
                String message = String.format("日期[%s]未找到任何CSV文件", targetDate);
                log.info(message);
                return message;
            }

            log.info("[明细导入] 开始全量导入 | 日期: {} | 文件数: {}", targetDate, csvFiles.size());

            int successCount = 0;
            int failCount = 0;
            StringBuilder resultBuilder = new StringBuilder();
            resultBuilder.append("全量导入结果:\n");

            // 逐个处理每个CSV文件
            for (Path csvFile : csvFiles) {
                try {
                    String result = processSingleCsvFile(csvFile, null, targetDate, tableName);
                    resultBuilder.append(String.format("文件[%s]: %s\n", csvFile.getFileName(), result));
                    successCount++;
                } catch (Exception e) {
                    String errorMsg = String.format("文件[%s]导入失败: %s", csvFile.getFileName(), e.getMessage());
                    resultBuilder.append(errorMsg).append("\n");
                    failCount++;
                    log.error("[明细导入] 文件导入失败: {}", csvFile.toString(), e);
                }
            }

            resultBuilder.append(String.format("\n总计: 成功 %d 个，失败 %d 个", successCount, failCount));
            String finalResult = resultBuilder.toString();
            log.info("[明细导入] 全量导入完成: {}", finalResult);
            return finalResult;

        } finally {
            redisLockUtil.unlock(lockKey);
        }
    }

    /**
     * 导入单个指标的CSV文件
     */
    private String importSingleMetricCsv(Integer metricId, LocalDate targetDate, String tableName) {
        String lockKey = "detail_import_" + metricId + "_" + targetDate;

        // 获取分布式锁
        if (!redisLockUtil.tryLockByValidityTime(lockKey)) {
            String message = String.format("指标[%d]在日期[%s]的导入任务正在执行中", metricId, targetDate);
            log.warn(message);
            return message;
        }

        try {
            // 扫描指定指标的CSV文件
            List<Path> csvFiles = scanCsvFiles(targetDate, metricId);
            if (csvFiles.isEmpty()) {
                String message = String.format("指标[%d]在日期[%s]未找到任何CSV文件", metricId, targetDate);
                log.info(message);
                return message;
            }

            log.info("[明细导入] 开始导入指标 | 指标ID: {} | 日期: {} | 文件数: {}", metricId, targetDate, csvFiles.size());

            int successCount = 0;
            int failCount = 0;
            StringBuilder resultBuilder = new StringBuilder();
            resultBuilder.append(String.format("指标[%d]导入结果:\n", metricId));

            // 逐个处理每个CSV文件
            for (Path csvFile : csvFiles) {
                try {
                    String result = processSingleCsvFile(csvFile, metricId, targetDate, tableName);
                    resultBuilder.append(String.format("文件[%s]: %s\n", csvFile.getFileName(), result));
                    successCount++;
                } catch (Exception e) {
                    String errorMsg = String.format("文件[%s]导入失败: %s", csvFile.getFileName(), e.getMessage());
                    resultBuilder.append(errorMsg).append("\n");
                    failCount++;
                    log.error("[明细导入] 文件导入失败: {}", csvFile.toString(), e);
                }
            }

            resultBuilder.append(String.format("\n总计: 成功 %d 个，失败 %d 个", successCount, failCount));
            String finalResult = resultBuilder.toString();
            log.info("[明细导入] 指标导入完成: {}", finalResult);
            return finalResult;

        } finally {
            redisLockUtil.unlock(lockKey);
        }
    }

    /**
     * 扫描CSV文件
     */
    private List<Path> scanCsvFiles(LocalDate targetDate, Integer metricId) {
        List<Path> csvFiles = new ArrayList<>();
        String dateStr = targetDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        try {
            Path baseDatePath = Paths.get(basePath, dateStr);
            if (!Files.exists(baseDatePath)) {
                log.warn("[文件扫描] 日期目录不存在: {}", baseDatePath);
                return csvFiles;
            }

            if (metricId != null) {
                // 扫描指定指标的文件
                Path metricPath = baseDatePath.resolve(metricId.toString());
                if (Files.exists(metricPath)) {
                    scanCsvFilesInDirectory(metricPath, csvFiles);
                }
            } else {
                // 扫描所有指标的文件
                Files.list(baseDatePath)
                        .filter(Files::isDirectory)
                        .forEach(metricDir -> scanCsvFilesInDirectory(metricDir, csvFiles));
            }

            log.info("[文件扫描] 扫描完成 | 日期: {} | 指标ID: {} | 文件数: {}",
                    targetDate, metricId, csvFiles.size());

        } catch (Exception e) {
            log.error("[文件扫描] 扫描CSV文件失败 | 日期: {} | 指标ID: {}", targetDate, metricId, e);
        }

        return csvFiles;
    }

    /**
     * 在指定目录中扫描CSV文件
     */
    private void scanCsvFilesInDirectory(Path directory, List<Path> csvFiles) {
        try {
            Files.walk(directory)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().toLowerCase().endsWith(".csv"))
                    .forEach(csvFiles::add);
        } catch (Exception e) {
            log.error("[文件扫描] 扫描目录失败: {}", directory, e);
        }
    }

    /**
     * 处理单个CSV文件
     */
    private String processSingleCsvFile(Path csvFile, Integer metricId, LocalDate targetDate, String tableName) {
        String taskId = IdUtil.simpleUUID();

        try {
            // 从文件路径中提取指标ID（如果未提供）
            if (metricId == null) {
                metricId = extractMetricIdFromPath(csvFile);
            }

            // 创建导入任务记录
            DetailImportTask task = createImportTask(taskId, metricId, targetDate, tableName,
                    csvFile.getFileName().toString(), Files.size(csvFile));
            task.setFilePath(csvFile.toString());
            detailImportTaskMapper.updateById(task);

            // 处理CSV文件导入
            return processFileImport(task, csvFile);

        } catch (Exception e) {
            log.error("[明细导入] 处理CSV文件失败 | 文件: {} | 错误: {}", csvFile, e.getMessage(), e);
            updateTaskStatus(taskId, DetailImportTask.Status.FAILED, e.getMessage());
            throw new RuntimeException("处理CSV文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从文件路径中提取指标ID
     */
    private Integer extractMetricIdFromPath(Path csvFile) {
        try {
            // 路径格式：/YYYY-MM-DD/metric_id/搜索/ads/协议类型+时间戳.csv
            Path parent = csvFile.getParent(); // ads
            if (parent != null) {
                parent = parent.getParent(); // 搜索
                if (parent != null) {
                    parent = parent.getParent(); // metric_id
                    if (parent != null) {
                        String metricIdStr = parent.getFileName().toString();
                        return Integer.parseInt(metricIdStr);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("[路径解析] 无法从路径中提取指标ID: {}", csvFile, e);
        }

        throw new RuntimeException("无法从文件路径中提取指标ID: " + csvFile);
    }

    /**
     * 创建导入任务记录
     */
    private DetailImportTask createImportTask(String taskId, Integer metricId, LocalDate targetDate,
                                              String tableName, String fileName, Long fileSize) {
        long currentTime = System.currentTimeMillis();

        // 如果没有指定表名，使用默认前缀（具体表名将在处理时根据协议类型确定）
        String finalTableName = StrUtil.isNotBlank(tableName) ? tableName : defaultTablePrefix + "auto";

        DetailImportTask task = DetailImportTask.builder()
                .taskId(taskId)
                .metricId(metricId)
                .targetDate(targetDate)
                .tableName(finalTableName)
                .fileName(fileName)
                .fileSize(fileSize)
                .totalRows(0L)
                .processedRows(0L)
                .successRows(0L)
                .failedRows(0L)
                .status(DetailImportTask.Status.PENDING.getCode())
                .startTime(currentTime)
                .createTime(currentTime)
                .modifyTime(currentTime)
                .build();

        detailImportTaskMapper.insert(task);
        log.info("[任务创建] 创建导入任务 | 任务ID: {} | 指标ID: {} | 文件: {}", taskId, metricId, fileName);

        return task;
    }

    /**
     * 处理上传文件导入（从文件路径）
     */
    private void processUploadFileImportFromPath(DetailImportTask task, Path tempFile) {
        try {
            // 更新任务状态为处理中
            updateTaskStatus(task.getTaskId(), DetailImportTask.Status.PROCESSING, null);

            // 处理文件导入
            String result = processFileImport(task, tempFile);

            // 清理临时文件
            Files.deleteIfExists(tempFile);

            log.info("[上传导入] 处理完成 | 任务ID: {} | 结果: {}", task.getTaskId(), result);

        } catch (Exception e) {
            log.error("[上传导入] 处理失败 | 任务ID: {} | 错误: {}", task.getTaskId(), e.getMessage(), e);
            updateTaskStatus(task.getTaskId(), DetailImportTask.Status.FAILED, e.getMessage());

            // 确保清理临时文件
            try {
                Files.deleteIfExists(tempFile);
            } catch (Exception cleanupException) {
                log.warn("[上传导入] 清理临时文件失败 | 文件: {} | 错误: {}", tempFile, cleanupException.getMessage());
            }
        }
    }

    /**
     * 处理上传文件导入（已废弃，保留用于兼容性）
     */
    @Deprecated
    private void processUploadFileImport(DetailImportTask task, MultipartFile file) {
        try {
            // 更新任务状态为处理中
            updateTaskStatus(task.getTaskId(), DetailImportTask.Status.PROCESSING, null);

            // 保存上传文件到临时目录
            Path tempFile = saveUploadedFile(file, task.getTaskId());

            // 更新任务文件路径
            task.setFilePath(tempFile.toString());
            detailImportTaskMapper.updateById(task);

            // 处理文件导入
            String result = processFileImport(task, tempFile);

            // 清理临时文件
            Files.deleteIfExists(tempFile);

            log.info("[上传导入] 处理完成 | 任务ID: {} | 结果: {}", task.getTaskId(), result);

        } catch (Exception e) {
            log.error("[上传导入] 处理失败 | 任务ID: {} | 错误: {}", task.getTaskId(), e.getMessage(), e);
            updateTaskStatus(task.getTaskId(), DetailImportTask.Status.FAILED, e.getMessage());
        }
    }

    /**
     * 保存上传文件到临时目录
     */
    private Path saveUploadedFile(MultipartFile file, String taskId) throws IOException {
        // 创建临时目录
        Path tempDir = Paths.get(tempPath);
        if (!Files.exists(tempDir)) {
            Files.createDirectories(tempDir);
        }

        // 生成临时文件名
        String tempFileName = taskId + "_" + file.getOriginalFilename();
        Path tempFile = tempDir.resolve(tempFileName);

        // 保存文件
        try (InputStream inputStream = file.getInputStream()) {
            Files.copy(inputStream, tempFile);
        }

        log.info("[文件保存] 上传文件已保存到临时目录 | 文件: {}", tempFile);
        return tempFile;
    }

    /**
     * 处理文件导入
     */
    private String processFileImport(DetailImportTask task, Path csvFile) {
        try {
            // 更新任务状态为处理中
            updateTaskStatus(task.getTaskId(), DetailImportTask.Status.PROCESSING, null);

            // 检查文件大小，如果超过阈值使用流式处理
            long fileSize = Files.size(csvFile);
            long streamThreshold = 100 * 1024 * 1024; // 100MB

            if (fileSize > streamThreshold) {
                log.info("[文件导入] 文件大小{}MB超过阈值，使用流式处理 | 任务ID: {}",
                    fileSize / 1024 / 1024, task.getTaskId());
                return processFileImportStream(task, csvFile);
            } else {
                log.info("[文件导入] 文件大小{}MB，使用常规处理 | 任务ID: {}",
                    fileSize / 1024 / 1024, task.getTaskId());
                return processFileImportRegular(task, csvFile);
            }

        } catch (Exception e) {
            log.error("[文件导入] 导入失败 | 任务ID: {} | 错误: {}", task.getTaskId(), e.getMessage(), e);
            updateTaskStatus(task.getTaskId(), DetailImportTask.Status.FAILED, e.getMessage());
            throw new RuntimeException("文件导入失败: " + e.getMessage(), e);
        }
    }

    /**
     * 常规文件导入处理（适用于小文件）
     */
    private String processFileImportRegular(DetailImportTask task, Path csvFile) throws IOException {
        // 解析CSV文件
        List<Map<String, Object>> csvData = parseCsvFile(csvFile, task);
        if (csvData.isEmpty()) {
            updateTaskStatus(task.getTaskId(), DetailImportTask.Status.FAILED, "CSV文件为空或格式错误");
            return "CSV文件为空或格式错误";
        }

        // 更新总行数
        task.setTotalRows((long) csvData.size());
        detailImportTaskMapper.updateById(task);

        // 按协议类型分组数据并分别导入
        Map<String, List<Map<String, Object>>> dataByProtocol = groupDataByProtocol(csvData);

        long totalSuccess = 0;
        long totalFailed = 0;
        StringBuilder resultBuilder = new StringBuilder();

        for (Map.Entry<String, List<Map<String, Object>>> entry : dataByProtocol.entrySet()) {
            String protocolType = entry.getKey();
            List<Map<String, Object>> protocolData = entry.getValue();

            try {
                // 为每个协议类型导入数据
                ProtocolImportResult result = importProtocolDataToDoris(task, protocolType, protocolData);
                totalSuccess += result.getSuccessCount();
                totalFailed += result.getFailedCount();

                resultBuilder.append(String.format("协议[%s]: 成功%d条, 失败%d条; ",
                        protocolType, result.getSuccessCount(), result.getFailedCount()));

            } catch (Exception e) {
                totalFailed += protocolData.size();
                resultBuilder.append(String.format("协议[%s]: 导入失败 - %s; ", protocolType, e.getMessage()));
                log.error("[协议导入] 协议[{}]导入失败 | 任务ID: {} | 错误: {}",
                        protocolType, task.getTaskId(), e.getMessage(), e);
            }
        }

        // 更新最终统计
        task.setSuccessRows(totalSuccess);
        task.setFailedRows(totalFailed);
        task.setProcessedRows((long) csvData.size());
        detailImportTaskMapper.updateById(task);

        // 更新任务状态
        if (totalFailed == 0) {
            updateTaskStatus(task.getTaskId(), DetailImportTask.Status.SUCCESS, null);
        } else if (totalSuccess > 0) {
            updateTaskStatus(task.getTaskId(), DetailImportTask.Status.PARTIAL_SUCCESS,
                    String.format("部分成功: 成功%d条, 失败%d条", totalSuccess, totalFailed));
        } else {
            updateTaskStatus(task.getTaskId(), DetailImportTask.Status.FAILED, "所有数据导入失败");
        }

        String finalResult = String.format("导入完成 | 总行数: %d | 成功: %d | 失败: %d | 详情: %s",
                csvData.size(), totalSuccess, totalFailed, resultBuilder.toString());
        log.info("[文件导入] {} | 任务ID: {}", finalResult, task.getTaskId());
        return finalResult;
    }

    /**
     * 流式文件导入处理（适用于大文件）
     */
    private String processFileImportStream(DetailImportTask task, Path csvFile) throws IOException {
        log.info("[流式导入] 开始流式处理大文件 | 任务ID: {} | 文件: {}", task.getTaskId(), csvFile.getFileName());

        long totalSuccess = 0;
        long totalFailed = 0;
        long totalProcessed = 0;
        Map<String, Long> protocolCounts = new HashMap<>();
        StringBuilder resultBuilder = new StringBuilder();

        try (BufferedReader reader = Files.newBufferedReader(csvFile, StandardCharsets.UTF_8)) {
            String headerLine = reader.readLine();
            if (headerLine == null) {
                updateTaskStatus(task.getTaskId(), DetailImportTask.Status.FAILED, "CSV文件为空");
                return "CSV文件为空";
            }

            // 解析头部
            String[] headers = parseCSVLine(headerLine);
            log.info("[流式导入] 解析头部 | 字段数: {} | 任务ID: {}", headers.length, task.getTaskId());

            // 批量处理数据
            List<Map<String, Object>> batch = new ArrayList<>();
            String line;
            int lineNumber = 1;

            while ((line = reader.readLine()) != null) {
                lineNumber++;
                try {
                    String[] values = parseCSVLine(line);
                    if (values.length != headers.length) {
                        log.warn("[流式导入] 行[{}]字段数不匹配 | 期望: {} | 实际: {} | 任务ID: {}",
                                lineNumber, headers.length, values.length, task.getTaskId());
                        totalFailed++;
                        continue;
                    }

                    Map<String, Object> rowData = new HashMap<>();
                    for (int i = 0; i < headers.length; i++) {
                        rowData.put(headers[i], values[i]);
                    }
                    batch.add(rowData);

                    // 当批次达到指定大小时处理
                    if (batch.size() >= streamBatchSize) {
                        StreamBatchResult batchResult = processStreamBatch(task, batch);
                        totalSuccess += batchResult.getSuccessCount();
                        totalFailed += batchResult.getFailedCount();
                        totalProcessed += batch.size();

                        // 更新协议统计
                        batchResult.getProtocolCounts().forEach((protocol, count) ->
                                protocolCounts.merge(protocol, count, Long::sum));

                        // 更新进度
                        task.setProcessedRows(totalProcessed);
                        detailImportTaskMapper.updateById(task);

                        // 清空批次，释放内存
                        batch.clear();
                    }

                } catch (Exception e) {
                    log.warn("[流式导入] 解析行[{}]失败 | 任务ID: {} | 错误: {}", lineNumber, task.getTaskId(), e.getMessage());
                    totalFailed++;
                }
            }

            // 处理剩余的数据
            if (!batch.isEmpty()) {
                StreamBatchResult batchResult = processStreamBatch(task, batch);
                totalSuccess += batchResult.getSuccessCount();
                totalFailed += batchResult.getFailedCount();
                totalProcessed += batch.size();

                batchResult.getProtocolCounts().forEach((protocol, count) ->
                        protocolCounts.merge(protocol, count, Long::sum));
            }

            // 构建结果信息
            protocolCounts.forEach((protocol, count) ->
                    resultBuilder.append(String.format("协议[%s]: %d条; ", protocol, count)));

            // 更新最终统计
            task.setTotalRows(totalProcessed);
            task.setSuccessRows(totalSuccess);
            task.setFailedRows(totalFailed);
            task.setProcessedRows(totalProcessed);
            detailImportTaskMapper.updateById(task);

            // 更新任务状态
            if (totalFailed == 0) {
                updateTaskStatus(task.getTaskId(), DetailImportTask.Status.SUCCESS, null);
            } else if (totalSuccess > 0) {
                updateTaskStatus(task.getTaskId(), DetailImportTask.Status.PARTIAL_SUCCESS,
                        String.format("部分成功: 成功%d条, 失败%d条", totalSuccess, totalFailed));
            } else {
                updateTaskStatus(task.getTaskId(), DetailImportTask.Status.FAILED, "所有数据导入失败");
            }

            String finalResult = String.format("流式导入完成 | 总行数: %d | 成功: %d | 失败: %d | 详情: %s",
                    totalProcessed, totalSuccess, totalFailed, resultBuilder.toString());
            log.info("[流式导入] {} | 任务ID: {}", finalResult, task.getTaskId());
            return finalResult;
        }
    }

    /**
     * 处理流式批次数据
     */
    private StreamBatchResult processStreamBatch(DetailImportTask task, List<Map<String, Object>> batch) {
        Map<String, Long> protocolCounts = new HashMap<>();
        long successCount = 0;
        long failedCount = 0;

        try {
            // 按协议类型分组数据
            Map<String, List<Map<String, Object>>> dataByProtocol = groupDataByProtocol(batch);

            for (Map.Entry<String, List<Map<String, Object>>> entry : dataByProtocol.entrySet()) {
                String protocolType = entry.getKey();
                List<Map<String, Object>> protocolData = entry.getValue();

                protocolCounts.put(protocolType, (long) protocolData.size());

                try {
                    // 为每个协议类型导入数据
                    ProtocolImportResult result = importProtocolDataToDoris(task, protocolType, protocolData);
                    successCount += result.getSuccessCount();
                    failedCount += result.getFailedCount();

                } catch (Exception e) {
                    failedCount += protocolData.size();
                    log.error("[流式导入] 协议[{}]批次导入失败 | 任务ID: {} | 错误: {}",
                            protocolType, task.getTaskId(), e.getMessage(), e);
                }
            }

        } catch (Exception e) {
            log.error("[流式导入] 批次处理失败 | 任务ID: {} | 错误: {}", task.getTaskId(), e.getMessage(), e);
            failedCount = batch.size();
        }

        return new StreamBatchResult(successCount, failedCount, protocolCounts);
    }

    /**
     * 解析CSV文件
     */
    private List<Map<String, Object>> parseCsvFile(Path csvFile, DetailImportTask task) throws IOException {
        List<Map<String, Object>> csvData = new ArrayList<>();

        try (BufferedReader reader = Files.newBufferedReader(csvFile, StandardCharsets.UTF_8)) {
            String headerLine = reader.readLine();
            if (headerLine == null) {
                throw new RuntimeException("CSV文件为空");
            }

            // 解析头部
            String[] headers = parseCSVLine(headerLine);
            log.info("[CSV解析] 解析头部 | 字段数: {} | 任务ID: {}", headers.length, task.getTaskId());

            // 解析数据行
            String line;
            int lineNumber = 1;
            while ((line = reader.readLine()) != null) {
                lineNumber++;
                try {
                    String[] values = parseCSVLine(line);
                    if (values.length != headers.length) {
                        log.warn("[CSV解析] 行[{}]字段数不匹配 | 期望: {} | 实际: {} | 任务ID: {}",
                                lineNumber, headers.length, values.length, task.getTaskId());
                        continue;
                    }

                    Map<String, Object> rowData = new HashMap<>();
                    for (int i = 0; i < headers.length; i++) {
                        rowData.put(headers[i], values[i]);
                    }
                    csvData.add(rowData);

                } catch (Exception e) {
                    log.warn("[CSV解析] 解析行[{}]失败 | 任务ID: {} | 错误: {}", lineNumber, task.getTaskId(), e.getMessage());
                }
            }

            log.info("[CSV解析] 解析完成 | 总行数: {} | 有效行数: {} | 任务ID: {}",
                    lineNumber - 1, csvData.size(), task.getTaskId());
        }

        return csvData;
    }

    /**
     * 解析CSV行（处理逗号分隔和引号转义）
     */
    private String[] parseCSVLine(String line) {
        List<String> fields = new ArrayList<>();
        StringBuilder currentField = new StringBuilder();
        boolean inQuotes = false;

        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);

            if (c == '"') {
                if (inQuotes && i + 1 < line.length() && line.charAt(i + 1) == '"') {
                    // 转义的引号
                    currentField.append('"');
                    i++; // 跳过下一个引号
                } else {
                    // 切换引号状态
                    inQuotes = !inQuotes;
                }
            } else if (c == ',' && !inQuotes) {
                // 字段分隔符
                fields.add(currentField.toString());
                currentField.setLength(0);
            } else {
                currentField.append(c);
            }
        }

        // 添加最后一个字段
        fields.add(currentField.toString());

        return fields.toArray(new String[0]);
    }

    /**
     * 按协议类型分组数据
     */
    private Map<String, List<Map<String, Object>>> groupDataByProtocol(List<Map<String, Object>> csvData) {
        Map<String, List<Map<String, Object>>> groupedData = new HashMap<>();

        for (Map<String, Object> row : csvData) {
            // 从CSV数据中提取协议类型
            String protocolType = extractProtocolTypeFromCsvRow(row);
            if (StrUtil.isNotBlank(protocolType)) {
                groupedData.computeIfAbsent(protocolType, k -> new ArrayList<>()).add(row);
            } else {
                log.warn("[数据分组] 无法识别协议类型的数据行: {}", row);
            }
        }

        log.info("[数据分组] 按协议分组完成 | 协议数: {} | 详情: {}",
                groupedData.size(),
                groupedData.entrySet().stream()
                        .map(entry -> entry.getKey() + "(" + entry.getValue().size() + "条)")
                        .collect(Collectors.joining(", ")));

        return groupedData;
    }

    /**
     * 从CSV行数据中提取协议类型
     */
    private String extractProtocolTypeFromCsvRow(Map<String, Object> row) {
        // 尝试从"协议类型编码"字段获取
        Object protocolTypeObj = row.get("协议类型编码");
        if (protocolTypeObj != null) {
            return protocolTypeObj.toString().trim();
        }

        // 尝试从"protocol_type"字段获取（英文字段名）
        protocolTypeObj = row.get("protocol_type");
        if (protocolTypeObj != null) {
            return protocolTypeObj.toString().trim();
        }

        // 如果都没有找到，记录警告
        log.warn("[协议提取] 未找到协议类型字段 | 可用字段: {}", row.keySet());
        return null;
    }

    /**
     * 导入单个协议的数据到Doris
     */
    private ProtocolImportResult importProtocolDataToDoris(DetailImportTask task, String protocolType,
                                                           List<Map<String, Object>> protocolData) {
        try {
            log.info("[协议导入] 开始导入协议数据 | 协议: {} | 数据量: {} | 任务ID: {}",
                    protocolType, protocolData.size(), task.getTaskId());

            // 1. 获取协议字段配置
            List<DetailFieldConfig> fieldConfigs = detailFieldConfigMapper.getExportFieldsByDataType(protocolType);
            if (fieldConfigs.isEmpty()) {
                throw new RuntimeException("未找到协议[" + protocolType + "]的字段配置");
            }

            // 2. 生成表名
            String tableName = generateTableName(task.getTableName(), protocolType);

            // 3. 验证和转换数据格式
            List<Map<String, Object>> validatedData = validateAndTransformData(protocolData, fieldConfigs, protocolType);

            // 4. 批量导入数据
            return batchImportToDoris(tableName, validatedData, fieldConfigs, task.getTaskId());

        } catch (Exception e) {
            log.error("[协议导入] 协议[{}]导入失败 | 任务ID: {} | 错误: {}",
                    protocolType, task.getTaskId(), e.getMessage(), e);
            return new ProtocolImportResult(0, protocolData.size(), e.getMessage());
        }
    }

    /**
     * 生成表名
     */
    private String generateTableName(String baseTableName, String protocolType) {
        // 如果基础表名包含"auto"，则使用协议类型生成表名
        if (baseTableName.contains("auto")) {
            return defaultTablePrefix + protocolType;
        } else {
            // 如果指定了具体表名，则在表名后加协议类型后缀
            return baseTableName + "_" + protocolType;
        }
    }

    /**
     * 验证和转换数据格式
     */
    private List<Map<String, Object>> validateAndTransformData(List<Map<String, Object>> protocolData,
                                                               List<DetailFieldConfig> fieldConfigs,
                                                               String protocolType) {
        List<Map<String, Object>> validatedData = new ArrayList<>();

        // 创建字段名映射（支持中英文字段名）
        Map<String, String> fieldMapping = createFieldMapping(fieldConfigs);

        for (Map<String, Object> row : protocolData) {
            Map<String, Object> transformedRow = new HashMap<>();

            // 添加通用字段
            transformedRow.put("metric_id", row.get("监控指标"));
            transformedRow.put("system_name", row.get("系统"));
            transformedRow.put("data_level", row.get("数据层级"));
            transformedRow.put("table_name", row.get("表名"));
            transformedRow.put("protocol_type", protocolType);
            transformedRow.put("account", row.get("指标账号"));

            // 添加协议特定字段
            for (DetailFieldConfig config : fieldConfigs) {
                String fieldName = config.getFieldName();
                Object value = row.get(fieldName);

                // 如果直接字段名没找到，尝试通过映射查找
                if (value == null && fieldMapping.containsKey(fieldName)) {
                    value = row.get(fieldMapping.get(fieldName));
                }

                transformedRow.put(fieldName, value != null ? value.toString() : "");
            }

            validatedData.add(transformedRow);
        }

        log.info("[数据转换] 协议[{}]数据转换完成 | 原始数据: {}条 | 转换后: {}条",
                protocolType, protocolData.size(), validatedData.size());

        return validatedData;
    }

    /**
     * 创建字段映射（中英文字段名对应关系）
     */
    private Map<String, String> createFieldMapping(List<DetailFieldConfig> fieldConfigs) {
        Map<String, String> mapping = new HashMap<>();

        // 这里可以根据需要添加中英文字段名的映射关系
        // 例如：mapping.put("calling_number", "主叫号码");

        return mapping;
    }

    /**
     * 批量导入数据到Doris（按协议分表）
     */
    private ProtocolImportResult batchImportToDoris(String tableName, List<Map<String, Object>> validatedData,
                                                    List<DetailFieldConfig> fieldConfigs, String taskId) {
        try {
            log.info("[Doris导入] 开始导入数据 | 表名: {} | 数据量: {} | 任务ID: {}",
                    tableName, validatedData.size(), taskId);

            long successCount = 0;
            long failedCount = 0;

            // 分批处理数据
            for (int i = 0; i < validatedData.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, validatedData.size());
                List<Map<String, Object>> batch = validatedData.subList(i, endIndex);

                try {
                    // 转换为Doris Stream Load格式
                    String streamLoadData = convertToProtocolStreamLoadFormat(batch, fieldConfigs);

                    // 执行Stream Load（带列映射）
                    sendDataWithColumnMapping(streamLoadData, tableName, fieldConfigs);

                    successCount += batch.size();
                    log.debug("[Doris导入] 批次导入成功 | 表名: {} | 批次: {}-{} | 成功数: {} | 任务ID: {}",
                            tableName, i + 1, endIndex, batch.size(), taskId);

                } catch (Exception e) {
                    failedCount += batch.size();
                    log.error("[Doris导入] 批次导入失败 | 表名: {} | 批次: {}-{} | 错误: {} | 任务ID: {}",
                            tableName, i + 1, endIndex, e.getMessage(), taskId);
                }
            }

            log.info("[Doris导入] 导入完成 | 表名: {} | 总数: {} | 成功: {} | 失败: {} | 任务ID: {}",
                    tableName, validatedData.size(), successCount, failedCount, taskId);

            return new ProtocolImportResult(successCount, failedCount, null);

        } catch (Exception e) {
            log.error("[Doris导入] 导入失败 | 表名: {} | 错误: {} | 任务ID: {}", tableName, e.getMessage(), taskId);
            return new ProtocolImportResult(0, validatedData.size(), e.getMessage());
        }
    }

    /**
     * 转换为协议专用的Stream Load格式
     */
    private String convertToProtocolStreamLoadFormat(List<Map<String, Object>> batch, List<DetailFieldConfig> fieldConfigs) {
        StringBuilder sb = new StringBuilder();

        // 获取协议类型
        String protocolType = "";
        if (!batch.isEmpty()) {
            protocolType = getStringValue(batch.get(0), "protocol_type");
        }

        // 获取表列定义
        List<String> tableColumns = dorisColumnMappingUtil.getProtocolTableColumns(protocolType, fieldConfigs);

        // 验证第一行数据的兼容性
        if (!batch.isEmpty()) {
            DorisColumnMappingUtil.ValidationResult validation =
                    dorisColumnMappingUtil.validateDataCompatibility(batch.get(0), tableColumns);

            if (!validation.isValid()) {
                log.warn("[数据验证] 协议[{}]数据格式不匹配 | 错误: {}", protocolType, validation.getErrorMessage());
                // 继续处理，但记录警告
            }
        }

        // 转换每一行数据
        for (Map<String, Object> row : batch) {
            String rowData = dorisColumnMappingUtil.convertRowToStreamLoadFormat(row, tableColumns);
            sb.append(rowData).append("\n");
        }

        // 记录转换信息
        log.info("[Stream Load格式] 协议[{}]数据转换完成 | 行数: {} | 列数: {} | 列定义: {}",
                protocolType, batch.size(), tableColumns.size(), String.join(",", tableColumns));

        return sb.toString();
    }

    /**
     * 执行带列映射的Stream Load
     */
    private void sendDataWithColumnMapping(String streamLoadData, String tableName, List<DetailFieldConfig> fieldConfigs) throws Exception {
        // 获取协议类型
        String protocolType = extractProtocolTypeFromTableName(tableName);

        // 生成列映射
        List<String> tableColumns = dorisColumnMappingUtil.getProtocolTableColumns(protocolType, fieldConfigs);
        String columnMapping = dorisColumnMappingUtil.generateFullColumnMapping(tableColumns);

        log.info("[Stream Load] 表名: {} | 列映射: {} | 数据行数: {}",
                tableName, columnMapping, streamLoadData.split("\n").length);

        // 使用带列映射的Stream Load
        dorisStreamLoadUtil.sendDataWithColumnMapping(streamLoadData, tableName, columnMapping);
    }

    /**
     * 从表名中提取协议类型
     */
    private String extractProtocolTypeFromTableName(String tableName) {
        // 表名格式：ops_detail_协议类型
        if (tableName.startsWith("ops_detail_")) {
            return tableName.substring("ops_detail_".length());
        }
        return tableName;
    }

    /**
     * 安全获取字符串值
     */
    private String getStringValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        return value != null ? value.toString() : "";
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(String taskId, DetailImportTask.Status status, String errorMessage) {
        try {
            long currentTime = System.currentTimeMillis();
            detailImportTaskMapper.updateTaskStatus(taskId, status.getCode(), errorMessage,
                    currentTime, currentTime);
            log.debug("[任务状态] 更新任务状态 | 任务ID: {} | 状态: {} | 错误: {}",
                    taskId, status.getDescription(), errorMessage);
        } catch (Exception e) {
            log.error("[任务状态] 更新任务状态失败 | 任务ID: {} | 错误: {}", taskId, e.getMessage(), e);
        }
    }

    @Override
    public String importOpsApiAccessResultCsv(MultipartFile file) {
        String taskId = IdUtil.simpleUUID();
        String lockKey = "ops_api_access_result_import_" + taskId;

        // 获取分布式锁
        if (!redisLockUtil.tryLockByValidityTime(lockKey)) {
            String message = "API访问结果导入任务正在执行中";
            log.warn(message);
            return message;
        }

        try {
            // 检查文件大小
            if (file.getSize() > maxFileSize) {
                String message = String.format("文件大小超过限制 | 当前大小: %d 字节 (%.2f MB) | 最大允许: %d 字节 (%.2f MB)",
                        file.getSize(), file.getSize() / 1024.0 / 1024.0,
                        maxFileSize, maxFileSize / 1024.0 / 1024.0);
                log.warn("[API访问结果导入] {}", message);
                return message;
            }

            log.info("[API访问结果导入] 开始处理 | 文件名: {} | 文件大小: {}",
                    file.getOriginalFilename(), file.getSize());

            // 检查文件大小，决定使用流式处理还是常规处理
            long fileSize = file.getSize();
            long streamThreshold = 100 * 1024 * 1024; // 100MB

            if (fileSize > streamThreshold) {
                log.info("[API访问结果导入] 文件大小{}MB超过阈值，使用流式处理", fileSize / 1024 / 1024);
                return processOpsApiAccessResultStreamImport(file);
            } else {
                log.info("[API访问结果导入] 文件大小{}MB，使用常规处理", fileSize / 1024 / 1024);
                return processOpsApiAccessResultRegularImport(file);
            }

        } catch (Exception e) {
            log.error("[API访问结果导入] 导入失败 | 错误: {}", e.getMessage(), e);
            return String.format("导入失败: %s", e.getMessage());
        } finally {
            redisLockUtil.unlock(lockKey);
        }
    }

    /**
     * 常规处理API访问结果导入（适用于小文件）
     */
    private String processOpsApiAccessResultRegularImport(MultipartFile file) throws IOException {
        // 解析CSV文件
        List<OpsApiAccessResult> resultList = parseOpsApiAccessResultCsv(file);
        if (resultList.isEmpty()) {
            String message = "CSV文件为空或格式错误";
            log.warn("[API访问结果导入] {}", message);
            return message;
        }

        // 使用Stream Load批量写入数据
        insertOpsApiAccessResultToDoris(resultList);

        String result = String.format("导入完成 | 总行数: %d | 成功: %d | 失败: %d",
                resultList.size(), resultList.size(), 0);
        log.info("[API访问结果导入] {}", result);
        return result;
    }

    /**
     * 流式处理API访问结果导入（适用于大文件）
     */
    private String processOpsApiAccessResultStreamImport(MultipartFile file) throws IOException {
        log.info("[API访问结果流式导入] 开始流式处理大文件 | 文件名: {}", file.getOriginalFilename());

        long totalSuccess = 0;
        long totalFailed = 0;
        long totalProcessed = 0;

        try (BufferedReader reader = new BufferedReader(new java.io.InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            String headerLine = reader.readLine();
            if (headerLine == null) {
                return "CSV文件为空";
            }

            // 解析头部
            String[] headers = parseCSVLine(headerLine);
            log.info("[API访问结果流式导入] 解析头部 | 字段数: {}", headers.length);

            // 创建字段索引映射
            Map<String, Integer> headerIndexMap = new HashMap<>();
            for (int i = 0; i < headers.length; i++) {
                headerIndexMap.put(headers[i].trim(), i);
            }

            // 检查必要字段是否存在
            if (!headerIndexMap.containsKey("metric_id")) {
                return "CSV文件缺少必要字段: metric_id";
            }

            // 批量处理数据
            List<OpsApiAccessResult> batch = new ArrayList<>();
            String line;
            int lineNumber = 1;

            while ((line = reader.readLine()) != null) {
                lineNumber++;
                try {
                    String[] values = parseCSVLine(line);
                    if (values.length != headers.length) {
                        log.warn("[API访问结果流式导入] 行[{}]字段数不匹配 | 期望: {} | 实际: {}",
                                lineNumber, headers.length, values.length);
                        totalFailed++;
                        continue;
                    }

                    OpsApiAccessResult result = convertCsvRowToOpsApiAccessResult(values, headerIndexMap);
                    if (result != null) {
                        batch.add(result);
                    } else {
                        totalFailed++;
                    }

                    // 当批次达到指定大小时处理
                    if (batch.size() >= streamBatchSize) {
                        try {
                            insertOpsApiAccessResultToDorisStream(batch);
                            totalSuccess += batch.size();
                            totalProcessed += batch.size();

                            log.debug("[API访问结果流式导入] 批次处理完成 | 批次大小: {} | 总处理: {}",
                                batch.size(), totalProcessed);
                        } catch (Exception e) {
                            log.error("[API访问结果流式导入] 批次写入失败 | 错误: {}", e.getMessage(), e);
                            totalFailed += batch.size();
                        }

                        // 清空批次，释放内存
                        batch.clear();
                    }

                } catch (Exception e) {
                    log.warn("[API访问结果流式导入] 解析行[{}]失败 | 错误: {}", lineNumber, e.getMessage());
                    totalFailed++;
                }
            }

            // 处理剩余的数据
            if (!batch.isEmpty()) {
                try {
                    insertOpsApiAccessResultToDorisStream(batch);
                    totalSuccess += batch.size();
                    totalProcessed += batch.size();
                } catch (Exception e) {
                    log.error("[API访问结果流式导入] 最后批次写入失败 | 错误: {}", e.getMessage(), e);
                    totalFailed += batch.size();
                }
            }

            String result = String.format("流式导入完成 | 总行数: %d | 成功: %d | 失败: %d",
                    totalProcessed + totalFailed, totalSuccess, totalFailed);
            log.info("[API访问结果流式导入] {}", result);
            return result;
        }
    }

    /**
     * 解析ops_api_access_result的CSV文件
     */
    private List<OpsApiAccessResult> parseOpsApiAccessResultCsv(MultipartFile file) throws IOException {
        List<OpsApiAccessResult> resultList = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new java.io.InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            String headerLine = reader.readLine();
            if (headerLine == null) {
                throw new RuntimeException("CSV文件为空");
            }

            // 解析头部
            String[] headers = parseCSVLine(headerLine);
            log.info("[CSV解析] 解析头部 | 字段数: {}", headers.length);

            // 创建字段索引映射
            Map<String, Integer> headerIndexMap = new HashMap<>();
            for (int i = 0; i < headers.length; i++) {
                headerIndexMap.put(headers[i].trim(), i);
            }

            // 检查必要字段是否存在
            if (!headerIndexMap.containsKey("metric_id")) {
                throw new RuntimeException("CSV文件缺少必要字段: metric_id");
            }

            // 解析数据行
            String line;
            int lineNumber = 1;
            while ((line = reader.readLine()) != null) {
                lineNumber++;
                try {
                    String[] values = parseCSVLine(line);
                    if (values.length != headers.length) {
                        log.warn("[CSV解析] 行[{}]字段数不匹配 | 期望: {} | 实际: {}",
                                lineNumber, headers.length, values.length);
                        continue;
                    }

                    OpsApiAccessResult result = convertCsvRowToOpsApiAccessResult(values, headerIndexMap);
                    if (result != null) {
                        resultList.add(result);
                    }

                } catch (Exception e) {
                    log.warn("[CSV解析] 解析行[{}]失败 | 错误: {}", lineNumber, e.getMessage());
                }
            }
        }

        log.info("[CSV解析] 解析完成 | 总行数: {}", resultList.size());
        return resultList;
    }

    /**
     * 将CSV行数据转换为OpsApiAccessResult实体
     */
    private OpsApiAccessResult convertCsvRowToOpsApiAccessResult(String[] values, Map<String, Integer> headerIndexMap) {
        try {
            OpsApiAccessResult result = new OpsApiAccessResult();

            // 从CSV中读取指标ID
            String metricId = getValueByHeader(values, headerIndexMap, "metric_id");
            if (StrUtil.isBlank(metricId)) {
                log.warn("[数据转换] metric_id字段为空，跳过该行");
                return null;
            }
            result.setMetricId(metricId);

            // 解析各个字段
            result.setStatDate(parseDate(getValueByHeader(values, headerIndexMap, "stat_date")));
            result.setAccount(getValueByHeader(values, headerIndexMap, "account"));
            result.setSystemName(getValueByHeader(values, headerIndexMap, "system_name"));
            result.setApiPath(getValueByHeader(values, headerIndexMap, "api_path"));
            result.setRequestTime(parseDateTime(getValueByHeader(values, headerIndexMap, "request_time")));
            result.setResponseStatus(parseShort(getValueByHeader(values, headerIndexMap, "response_status")));
            result.setCostTime(parseInteger(getValueByHeader(values, headerIndexMap, "cost_time")));
            result.setResponseData(getValueByHeader(values, headerIndexMap, "response_data"));
            result.setStatCount(parseLong(getValueByHeader(values, headerIndexMap, "stat_count")));
            result.setSuccess(parseBoolean(getValueByHeader(values, headerIndexMap, "is_success")));
            result.setErrorCode(getValueByHeader(values, headerIndexMap, "error_code"));
            result.setKv(parseBoolean(getValueByHeader(values, headerIndexMap, "is_kv")));
            result.setKvContent(getValueByHeader(values, headerIndexMap, "kv_content"));
            result.setIsDynamic(parseBoolean(getValueByHeader(values, headerIndexMap, "is_dynamic")));
            result.setRedundantField(getValueByHeader(values, headerIndexMap, "redundant_field"));

            // 解析JSON参数
            String paramsStr = getValueByHeader(values, headerIndexMap, "params");
            if (StrUtil.isNotBlank(paramsStr)) {
                try {
                    // 尝试解析为JSONObject
                    result.setParams(parseParamsJson(paramsStr));
                } catch (Exception e) {
                    log.warn("[数据转换] 解析params JSON失败: {} | 指标ID: {}", paramsStr, metricId);
                    result.setParams(null);
                }
            }

            return result;

        } catch (Exception e) {
            log.error("[数据转换] 转换CSV行数据失败 | 错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据字段名获取值
     */
    private String getValueByHeader(String[] values, Map<String, Integer> headerIndexMap, String headerName) {
        Integer index = headerIndexMap.get(headerName);
        if (index != null && index < values.length) {
            String value = values[index];
            return StrUtil.isNotBlank(value) ? value.trim() : null;
        }
        return null;
    }

    /**
     * 解析日期
     */
    private java.util.Date parseDate(String dateStr) {
        if (StrUtil.isBlank(dateStr)) {
            return null;
        }
        try {
            // 支持多种日期格式
            if (dateStr.contains("-")) {
                return java.sql.Date.valueOf(dateStr);
            } else if (dateStr.length() == 8) {
                // yyyyMMdd格式
                return new java.text.SimpleDateFormat("yyyyMMdd").parse(dateStr);
            }
            return null;
        } catch (Exception e) {
            log.warn("[日期解析] 解析日期失败: {}", dateStr);
            return null;
        }
    }

    /**
     * 解析日期时间
     */
    private java.util.Date parseDateTime(String dateTimeStr) {
        if (StrUtil.isBlank(dateTimeStr)) {
            return null;
        }
        try {
            // 支持多种日期时间格式
            if (dateTimeStr.contains("T")) {
                return java.sql.Timestamp.valueOf(dateTimeStr.replace("T", " "));
            } else if (dateTimeStr.contains(" ")) {
                return java.sql.Timestamp.valueOf(dateTimeStr);
            }
            return null;
        } catch (Exception e) {
            log.warn("[日期时间解析] 解析日期时间失败: {}", dateTimeStr);
            return null;
        }
    }

    /**
     * 解析Short类型
     */
    private Short parseShort(String value) {
        if (StrUtil.isBlank(value)) {
            return null;
        }
        try {
            return Short.valueOf(value);
        } catch (Exception e) {
            log.warn("[Short解析] 解析Short失败: {}", value);
            return null;
        }
    }

    /**
     * 解析Integer类型
     */
    private Integer parseInteger(String value) {
        if (StrUtil.isBlank(value)) {
            return null;
        }
        try {
            return Integer.valueOf(value);
        } catch (Exception e) {
            log.warn("[Integer解析] 解析Integer失败: {}", value);
            return null;
        }
    }

    /**
     * 解析Long类型
     */
    private Long parseLong(String value) {
        if (StrUtil.isBlank(value)) {
            return 0L;
        }
        try {
            return Long.valueOf(value);
        } catch (Exception e) {
            log.warn("[Long解析] 解析Long失败: {}", value);
            return 0L;
        }
    }

    /**
     * 解析Boolean类型
     */
    private Boolean parseBoolean(String value) {
        if (StrUtil.isBlank(value)) {
            return null;
        }
        try {
            return "true".equalsIgnoreCase(value) || "1".equals(value);
        } catch (Exception e) {
            log.warn("[Boolean解析] 解析Boolean失败: {}", value);
            return null;
        }
    }

    /**
     * 解析params JSON字段
     * 支持JSON对象和JSON数组两种格式
     */
    private JSONObject parseParamsJson(String paramsStr) {
        if (StrUtil.isBlank(paramsStr)) {
            return null;
        }

        try {
            // 去除首尾空白字符
            paramsStr = paramsStr.trim();

            // 判断是否为JSON数组
            if (paramsStr.startsWith("[") && paramsStr.endsWith("]")) {
                // 如果是JSON数组，将其包装为JSONObject
                JSONObject wrapper = new JSONObject();
                wrapper.put("queries", com.alibaba.fastjson.JSON.parseArray(paramsStr));
                log.debug("[JSON解析] 检测到JSON数组，已包装为对象 | 数组长度: {}",
                    wrapper.getJSONArray("queries").size());
                return wrapper;
            } else if (paramsStr.startsWith("{") && paramsStr.endsWith("}")) {
                // 如果是JSON对象，直接解析
                return JSONObject.parseObject(paramsStr);
            } else {
                // 如果不是标准JSON格式，尝试作为字符串值处理
                JSONObject wrapper = new JSONObject();
                wrapper.put("raw", paramsStr);
                log.debug("[JSON解析] 非标准JSON格式，作为字符串处理");
                return wrapper;
            }
        } catch (Exception e) {
            log.warn("[JSON解析] 解析params失败，将作为字符串处理 | 原始数据: {} | 错误: {}",
                paramsStr.length() > 200 ? paramsStr.substring(0, 200) + "..." : paramsStr,
                e.getMessage());

            // 解析失败时，将原始字符串作为值保存
            JSONObject fallback = new JSONObject();
            fallback.put("raw", paramsStr);
            return fallback;
        }
    }

    /**
     * 使用Stream Load将OpsApiAccessResult列表写入Doris数据库
     *
     * @param resultList API访问结果列表
     */
    private void insertOpsApiAccessResultToDoris(List<OpsApiAccessResult> resultList) {
        if (resultList == null || resultList.isEmpty()) {
            log.warn("[Stream Load] 没有数据需要写入Doris");
            return;
        }

        try {
            // 将对象列表转换为表格格式的文本
            String tableData = convertOpsApiAccessResultToTableFormat(resultList);

            // 写入Doris数据库
            dorisStreamLoadUtil.sendData(tableData, OPS_API_ACCESS_RESULT_TABLE);
            log.info("[Stream Load] 成功将 {} 条数据写入 Doris 表 {}", resultList.size(), OPS_API_ACCESS_RESULT_TABLE);
        } catch (Exception e) {
            log.error("[Stream Load] 将数据写入Doris时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("Stream Load写入失败: " + e.getMessage(), e);
        }
    }

    /**
     * 流式写入OpsApiAccessResult列表到Doris数据库（优化内存使用）
     *
     * @param resultList API访问结果列表
     */
    private void insertOpsApiAccessResultToDorisStream(List<OpsApiAccessResult> resultList) {
        if (resultList == null || resultList.isEmpty()) {
            log.warn("[Stream Load] 没有数据需要写入Doris");
            return;
        }

        try {
            // 使用StringBuilder进行流式转换，避免大字符串拼接
            StringBuilder sb = new StringBuilder();

            for (OpsApiAccessResult data : resultList) {
                // 按照数据库表字段顺序构建数据行
                sb.append(formatFieldValue(data.getMetricId())).append("\t");
                sb.append(formatFieldValue(data.getStatDate())).append("\t");
                sb.append(formatFieldValue(data.getAccount())).append("\t");
                sb.append(formatFieldValue(data.getSystemName())).append("\t");
                sb.append(formatFieldValue(data.getApiPath())).append("\t");
                sb.append(formatFieldValue(data.getRequestTime())).append("\t");
                sb.append(formatFieldValue(data.getResponseStatus())).append("\t");
                sb.append(formatFieldValue(data.getCostTime())).append("\t");
                sb.append(formatFieldValue(data.getResponseData())).append("\t");
                sb.append(formatFieldValue(data.getStatCount())).append("\t");
                sb.append(formatFieldValue(data.getSuccess())).append("\t");
                sb.append(formatFieldValue(data.getErrorCode())).append("\t");
                sb.append(formatFieldValue(data.isKv())).append("\t");
                sb.append(formatFieldValue(data.getKvContent())).append("\t");
                sb.append(formatFieldValue(data.getParams())).append("\t");
                sb.append(formatFieldValue(data.getIsDynamic())).append("\t");
                sb.append(formatFieldValue(data.getRedundantField()));
                sb.append("\n");
            }

            // 写入Doris数据库
            dorisStreamLoadUtil.sendData(sb.toString(), OPS_API_ACCESS_RESULT_TABLE);
            log.debug("[Stream Load] 成功将 {} 条数据写入 Doris 表 {}", resultList.size(), OPS_API_ACCESS_RESULT_TABLE);
        } catch (Exception e) {
            log.error("[Stream Load] 将数据写入Doris时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("Stream Load写入失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将OpsApiAccessResult对象列表转换为表格格式的文本
     *
     * @param resultList API访问结果列表
     * @return 表格格式的文本数据
     */
    private String convertOpsApiAccessResultToTableFormat(List<OpsApiAccessResult> resultList) {
        StringBuilder sb = new StringBuilder();

        resultList.forEach(data -> {
            // 按照数据库表字段顺序构建数据行
            sb.append(formatFieldValue(data.getMetricId())).append("\t");
            sb.append(formatFieldValue(data.getStatDate())).append("\t");
            sb.append(formatFieldValue(data.getAccount())).append("\t");
            sb.append(formatFieldValue(data.getSystemName())).append("\t");
            sb.append(formatFieldValue(data.getApiPath())).append("\t");
            sb.append(formatFieldValue(data.getRequestTime())).append("\t");
            sb.append(formatFieldValue(data.getResponseStatus())).append("\t");
            sb.append(formatFieldValue(data.getCostTime())).append("\t");
            sb.append(formatFieldValue(data.getResponseData())).append("\t");
            sb.append(formatFieldValue(data.getStatCount())).append("\t");
            sb.append(formatFieldValue(data.getSuccess())).append("\t");
            sb.append(formatFieldValue(data.getErrorCode())).append("\t");
            sb.append(formatFieldValue(data.isKv())).append("\t");
            sb.append(formatFieldValue(data.getKvContent())).append("\t");
            sb.append(formatFieldValue(data.getParams())).append("\t");
            sb.append(formatFieldValue(data.getIsDynamic())).append("\t");
            sb.append(formatFieldValue(data.getRedundantField()));

            sb.append("\n");
        });

        // 删除最后一个多余的换行符
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }

        return sb.toString();
    }

    /**
     * 格式化字段值，处理null值和特殊字符
     */
    private String formatFieldValue(Object value) {
        if (value == null) {
            return "";
        }

        if (value instanceof java.util.Date) {
            // 日期格式化
            java.text.SimpleDateFormat sdf;
            if (value instanceof java.sql.Date) {
                sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
            } else {
                sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            }
            return sdf.format((java.util.Date) value);
        }

        if (value instanceof Boolean) {
            return ((Boolean) value) ? "1" : "0";
        }

        if (value instanceof JSONObject) {
            return ((JSONObject) value).toJSONString();
        }

        // 处理字符串中的特殊字符
        String str = value.toString();
        // 替换制表符和换行符，避免影响Stream Load格式
        str = str.replace("\t", " ").replace("\n", " ").replace("\r", " ");
        return str;
    }

    /**
     * 协议导入结果内部类
     */
    private static class ProtocolImportResult {
        private final long successCount;
        private final long failedCount;
        private final String errorMessage;

        public ProtocolImportResult(long successCount, long failedCount, String errorMessage) {
            this.successCount = successCount;
            this.failedCount = failedCount;
            this.errorMessage = errorMessage;
        }

        public long getSuccessCount() {
            return successCount;
        }

        public long getFailedCount() {
            return failedCount;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }

    /**
     * 流式批次处理结果内部类
     */
    private static class StreamBatchResult {
        private final long successCount;
        private final long failedCount;
        private final Map<String, Long> protocolCounts;

        public StreamBatchResult(long successCount, long failedCount, Map<String, Long> protocolCounts) {
            this.successCount = successCount;
            this.failedCount = failedCount;
            this.protocolCounts = protocolCounts;
        }

        public long getSuccessCount() {
            return successCount;
        }

        public long getFailedCount() {
            return failedCount;
        }

        public Map<String, Long> getProtocolCounts() {
            return protocolCounts;
        }
    }

}
