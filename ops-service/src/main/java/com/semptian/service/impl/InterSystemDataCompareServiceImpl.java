package com.semptian.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.meiya.whalex.util.JsonUtil;
import com.semptian.component.DorisStreamLoadUtil;
import com.semptian.entity.BusinessApiConfig;
import com.semptian.entity.BusinessMetricConfig;
import com.semptian.entity.OpsApiAccessResult;
import com.semptian.entity.OpsApiAccessResultDifference;
import com.semptian.service.*;
import com.semptian.service.impl.comparator.MetricKvDataComparatorFactory;
import com.semptian.service.impl.parser.MetricKvDataParserFactory;
import com.semptian.util.JsonEscapeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统间一致性结果对比实现
 *
 * <AUTHOR> Hu
 * @since 2025/5/20
 */
@Service
@Slf4j
public class InterSystemDataCompareServiceImpl implements InterSystemDataCompareService {

    @Value("${doris.result_difference_table}")
    String resultDifferenceTable = "ops_api_access_result_difference";

    @Resource
    BusinessMetricConfigService businessMetricConfigService;

    @Resource
    BusinessApiConfigService businessApiConfigService;

    @Resource
    OpsApiAccessResultService opsApiAccessResultService;

    @Resource
    OpsApiAccessResultDifferenceService  opsApiAccessResultDifferenceService;

    @Resource
    DorisStreamLoadUtil dorisStreamLoad;

    @Resource
    private MetricKvDataParserFactory metricKvDataParserFactory;

    @Resource
    private MetricKvDataComparatorFactory metricKvDataComparatorFactory;

    @Override
    public List<OpsApiAccessResultDifference> compareResult(Integer metricId, LocalDate startDate,LocalDate endDate, String account) {
        log.info("compareResult start, metricId: {}, startDate: {}, endDate: {}, account: {}", metricId, startDate, endDate, account);

        // 获取指标信息
        BusinessMetricConfig metricConfig = businessMetricConfigService.getByIdWithService(metricId);
        if (metricConfig == null) {
            log.error("metricId :{} is not exists", metricId);
            return Collections.emptyList();
        }

        //获取 api config配置数据
        List<BusinessApiConfig> apiConfigList = businessApiConfigService.getApiConfigByMetricId(metricId);
        if (CollUtil.isEmpty(apiConfigList)) {
            log.error("metricId :{} not found api config", metricId);
            return Collections.emptyList();
        }

        //判断当前指标是比较总数还是kv
        Boolean isKv = apiConfigList.get(0).getIsKv();

        //删除对应指标时间范围内的结果对比数据,如果指定了账号则还需要添加指定账号作为条件
        opsApiAccessResultDifferenceService.deleteByMetricIdAndTimeRange(metricId, startDate, endDate, account);

        // 获取指定时间范围内的所有日期
        List<LocalDate> dateList = getDatesBetween(startDate, startDate);

        //需要写入的差异结果数据集合
        List<OpsApiAccessResultDifference> resultList = new ArrayList<>();

        for (LocalDate localDate : dateList) {
            //查询指标对应结果数据,根据指标ID、时间范围、账号条件查询
            List<OpsApiAccessResult> list = opsApiAccessResultService.lambdaQuery()
                    .eq(OpsApiAccessResult::getMetricId, metricId.toString())
                    .eq(OpsApiAccessResult::getStatDate, localDate).list();

            if (StrUtil.isNotEmpty(account)) {
                list = list.stream().filter(result -> result.getAccount().equals(account)).collect(Collectors.toList());
            }

            if (CollUtil.isEmpty(list)) {
                log.info("metricId :{} , statDate:{} , account:{} ,  no collect data ", metricId, localDate, account);
                continue;
            }

            compareResultData(metricId, localDate, list, isKv, metricConfig, apiConfigList, resultList);
        }

        if (CollUtil.isNotEmpty(resultList)) {
            try {
                // 将对象列表转换为表格格式的文本
                String tableData = convertResultsToTableFormat(resultList);

                // 写入Doris数据库
                dorisStreamLoad.sendData(tableData, resultDifferenceTable);
                log.info("load data to Doris table {}, size: {}", resultDifferenceTable, resultList.size());
            } catch (Exception e) {
                log.error("load data to Doris table {} error: ", resultDifferenceTable, e);
            }
        }

        log.info("compareResult end, metricId: {}, startDate: {}, endDate: {}, account: {}, different data size:{} ", metricId, startDate, endDate, account, resultList.size());
        return resultList;
    }

    /**
     * 获取指定日期范围内的所有日期
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日期列表
     */
    private List<LocalDate> getDatesBetween(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> dates = new ArrayList<>();
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            dates.add(currentDate);
            currentDate = currentDate.plusDays(1);
        }
        return dates;
    }

    private void compareResultData(Integer metricId, LocalDate localDate, List<OpsApiAccessResult> list, Boolean isKv, BusinessMetricConfig metricConfig, List<BusinessApiConfig> apiConfigList, List<OpsApiAccessResultDifference> resultList) {
        //结果数据按照账号进行分组,获取账号在不同system_name下的结果数据
        Map<String, List<OpsApiAccessResult>> accountResultMap = list.stream().collect(Collectors.groupingBy(OpsApiAccessResult::getAccount));

        for (Map.Entry<String, List<OpsApiAccessResult>> entry : accountResultMap.entrySet()) {
            String currentAccount = entry.getKey();
            List<OpsApiAccessResult> accountResults = entry.getValue();

            // 如果是对比kv数据，需要获取kv_content的内容进行计算
            if (isKv) {
                // 解析kv_content并比较不同系统的值
                // 按照system_name分组，得到每个系统的结果数据
                for (OpsApiAccessResult accountResult : accountResults) {
                    if (StrUtil.isNotEmpty(accountResult.getKvContent())) {
                        accountResult.setKvContent(JsonEscapeUtils.escapeJson(accountResult.getKvContent()));
                    }
                }

                Map<String, OpsApiAccessResult> systemResultMap = accountResults.stream().collect(Collectors.toMap(OpsApiAccessResult::getSystemName, accessResult -> accessResult));

                compareKvData(metricId, localDate, metricConfig, apiConfigList, resultList, systemResultMap, currentAccount);
            } else {
                compareStatCountData(metricId, localDate, metricConfig, resultList, accountResults, currentAccount);
            }
        }
    }

    /**
     * 解析并对比各个系统的kv_content数据
     */
    private void compareKvData(Integer metricId, LocalDate localDate, BusinessMetricConfig metricConfig, List<BusinessApiConfig> apiConfigList, List<OpsApiAccessResultDifference> resultList, Map<String, OpsApiAccessResult> systemResultMap, String currentAccount) {
        //解析kv_content数据
        Map<String, Map<String, Integer>> systemKvMap = parseData(metricId, apiConfigList, systemResultMap);

        List<String> diffData;

        //如果只有一个系统有数据且kv_content为空则判定为不一致
        if (systemResultMap.size() == 1 && systemKvMap.values().stream().allMatch(Map::isEmpty)) {
            diffData = Lists.newArrayList("");
        }else {
            // 对比不同系统的kv数据
            diffData  = getDiffData(metricId, systemKvMap);
        }

        // 如果存在差异，创建差异结果对象
        if (!diffData.isEmpty()) {
            Map<String, Object> content = new HashMap<>();
            for (OpsApiAccessResult value : systemResultMap.values()) {

                JSONArray array = null;
                try {
                    array = JSONArray.parseArray(value.getKvContent());
                }catch (Exception e) {
                    log.error("parse metricId:{} account:{} kv content:{} error ", metricId, value.getAccount(), value.getKvContent(), e);
                }
                content.put(value.getSystemName(), array);
            }

            OpsApiAccessResultDifference difference = OpsApiAccessResultDifference.builder()
                    .metricId(String.valueOf(metricId))
                    .metricName(metricConfig.getMetricModelName() + "-" + metricConfig.getMetricName())
                    .statDate(Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant()))
                    .account(currentAccount)
                    .isKv(1)
                    .kvContent(JsonUtil.objectToStr(content))
                    .differentKvContent(String.join(",", diffData))
                    .build();

            resultList.add(difference);
        }
    }

    private Map<String, Map<String, Integer>> parseData(Integer metricId, List<BusinessApiConfig> apiConfigList, Map<String, OpsApiAccessResult> systemResultMap) {
        // 遍历每个系统的结果数据并解析

        // 存储每个系统解析后的kv数据
        Map<String, Map<String, Integer>> systemKvMap = new HashMap<>();

        for (Map.Entry<String, OpsApiAccessResult> entry : systemResultMap.entrySet()) {
            String systemName = entry.getKey();
            OpsApiAccessResult opsApiAccessResult = entry.getValue();

            MetricKvDataParser parser = metricKvDataParserFactory.getParser(metricId, systemName);
            parser.parseData(apiConfigList, opsApiAccessResult, systemKvMap);
        }

        return systemKvMap;
    }


    private List<String> getDiffData(Integer metricId, Map<String, Map<String, Integer>> systemKvMap) {
        MetricKvDataComparator comparator = metricKvDataComparatorFactory.getComparator(metricId);

        return comparator.getDiffData(systemKvMap);
    }

    private void compareStatCountData(Integer metricId, LocalDate localDate, BusinessMetricConfig metricConfig, List<OpsApiAccessResultDifference> resultList, List<OpsApiAccessResult> accountResults, String currentAccount) {
        // 对比总数数据，直接判断不同系统下的statCount是否相同
        Long statCount = null;
        boolean isSame = true;

        if (accountResults.size() > 1) {
            //  遍历不同系统的结果数据
            for (OpsApiAccessResult result : accountResults) {
                if (statCount == null) {
                    statCount = result.getStatCount();
                }

                if (result.getStatCount() != statCount) {
                    isSame = false;
                }
            }
        }else {
            // 如果只有一个系统数据,则确定不一致
            isSame = false;
        }

        if (!isSame) {
            Map<String, String> content = new HashMap<>();
            for (OpsApiAccessResult accountResult : accountResults) {
                content.put(accountResult.getSystemName(), String.valueOf(accountResult.getStatCount()));
            }

            // 创建差异结果对象并保存
            OpsApiAccessResultDifference difference = OpsApiAccessResultDifference.builder()
                    .metricId(String.valueOf(metricId))
                    .metricName(metricConfig.getMetricModelName() + "-" + metricConfig.getMetricName())
                    .statDate(Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant()))
                    .account(currentAccount)
                    .isKv(0)
                    .kvContent(JsonUtil.objectToStr(content))
                    .differentKvContent("")
                    .build();
            resultList.add(difference);
        }
    }

    /**
     * 将结果对象列表转换为表格格式的文本
     *
     * @param resultList API访问结果列表
     * @return 表格格式的文本数据
     */
    private String convertResultsToTableFormat(List<OpsApiAccessResultDifference> resultList) {
        StringBuilder sb = new StringBuilder();

        resultList.forEach(data -> {
            // 获取对象的所有字段
            Field[] fields = data.getClass().getDeclaredFields();

            // 处理每个字段
            for (Field field : fields) {
                // 跳过serialVersionUID字段
                if ("serialVersionUID".equals(field.getName())) {
                    continue;
                }

                // 设置字段可访问
                field.setAccessible(true);

                try {
                    // 获取字段值并添加到结果中，使用制表符分隔
                    if ("statDate".equals(field.getName())) {
                        sb.append(DateUtil.formatDate((Date) field.get(data))).append("\t");
                    }else {
                        sb.append(field.get(data)).append("\t");
                    }
                } catch (IllegalAccessException e) {
                    log.error("获取字段{}的值时发生错误: ", field.getName(), e);
                    // 当发生错误时，添加空值
                    sb.append("\t");
                }
            }

            // 删除最后一个多余的制表符并添加换行符
            if (sb.length() > 0) {
                sb.deleteCharAt(sb.length() - 1);
            }
            sb.append("\n");
        });

        // 删除最后一个多余的换行符
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }

        return sb.toString();
    }
}
