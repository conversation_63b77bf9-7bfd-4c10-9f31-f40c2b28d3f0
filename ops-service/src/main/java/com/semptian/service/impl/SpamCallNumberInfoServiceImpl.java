package com.semptian.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.semptian.config.SpamCallConfig;
import com.semptian.entity.SpamCallNumberInfo;
import com.semptian.mapper.SpamCallNumberInfoMapper;
import com.semptian.service.SpamCallNumberInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.semptian.util.BloomFilter;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 骚扰号码信息服务实现类
 */
@Slf4j
@Service
public class SpamCallNumberInfoServiceImpl extends ServiceImpl<SpamCallNumberInfoMapper, SpamCallNumberInfo> implements SpamCallNumberInfoService {

    @Resource
    private SpamCallNumberInfoMapper spamCallNumberInfoMapper;

    @Resource
    private SpamCallConfig spamCallConfig;

    // 使用布隆过滤器优化内存使用
    private volatile BloomFilter bloomFilter;

    // 备用精确查询缓存（仅缓存热点数据）
    private final Map<String, Boolean> hotNumbersCache = new ConcurrentHashMap<>();

    // 上次缓存更新时间
    private final AtomicLong lastCacheUpdateTime = new AtomicLong(0);

    @Override
    public List<SpamCallNumberInfo> getAllActiveSpamNumbers() {
        return spamCallNumberInfoMapper.getAllActiveSpamNumbers(spamCallConfig.getDatabaseName());
    }

    @Override
    public Set<String> getAllActiveSpamNumbersSet() {
        // 确保布隆过滤器已初始化
        ensureBloomFilterInitialized();

        // 注意：此方法仅为兼容性保留，实际使用中应避免返回完整集合
        // 建议使用 isSpamNumber 方法进行单个号码检查
        log.warn("getAllActiveSpamNumbersSet方法可能消耗大量内存，建议使用isSpamNumber方法");

        // 返回空集合，强制使用isSpamNumber方法
        return Collections.emptySet();
    }
    
    /**
     * 确保布隆过滤器已初始化
     */
    private void ensureBloomFilterInitialized() {
        long currentTime = System.currentTimeMillis();
        long cacheExpirationTime = spamCallConfig.getCacheExpirationHours() * 60 * 60 * 1000L;

        if (bloomFilter == null || (currentTime - lastCacheUpdateTime.get()) > cacheExpirationTime) {
            synchronized (this) {
                // 双重检查锁定
                if (bloomFilter == null || (currentTime - lastCacheUpdateTime.get()) > cacheExpirationTime) {
                    initializeBloomFilter();
                }
            }
        }
    }

    /**
     * 初始化布隆过滤器
     */
    private void initializeBloomFilter() {
        log.info("初始化骚扰号码布隆过滤器，使用数据库：{}", spamCallConfig.getDatabaseName());

        // 创建新的布隆过滤器
        BloomFilter newBloomFilter = new BloomFilter(
                spamCallConfig.getExpectedInsertions(),
                spamCallConfig.getFalsePositiveProbability());

        // 分批加载数据，避免内存溢出
        int batchSize = spamCallConfig.getBatchSize();
        int offset = 0;
        int totalNumbers = 0;

        while (true) {
            List<SpamCallNumberInfo> batch = getBatchSpamNumbers(offset, batchSize);
            if (batch.isEmpty()) {
                break;
            }

            // 处理批次数据
            for (SpamCallNumberInfo info : batch) {
                if (info.getPhoneNumber() != null) {
                    // 处理可能包含多个号码的情况（逗号分隔）
                    String[] numbers = info.getPhoneNumber().split(",");
                    for (String number : numbers) {
                        String trimmedNumber = number.trim();
                        if (!trimmedNumber.isEmpty()) {
                            newBloomFilter.add(trimmedNumber);
                            totalNumbers++;
                        }
                    }
                }
            }

            offset += batchSize;

            // 记录进度
            if (offset % 5000 == 0) {
                log.debug("已处理 {} 条记录，累计号码 {} 个", offset, totalNumbers);
            }
        }

        // 原子性替换布隆过滤器
        this.bloomFilter = newBloomFilter;
        this.lastCacheUpdateTime.set(System.currentTimeMillis());

        // 清理热点缓存
        hotNumbersCache.clear();

        log.info("骚扰号码布隆过滤器初始化完成，共处理 {} 个号码，内存使用约 {} KB",
                totalNumbers, newBloomFilter.getMemoryUsage() / 1024);
    }

    @Override
    public boolean isSpamNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return false;
        }

        String trimmedNumber = phoneNumber.trim();

        // 1. 先检查热点缓存
        Boolean cachedResult = hotNumbersCache.get(trimmedNumber);
        if (cachedResult != null) {
            return cachedResult;
        }

        // 2. 确保布隆过滤器已初始化
        ensureBloomFilterInitialized();

        // 3. 使用布隆过滤器快速排除
        if (!bloomFilter.mightContain(trimmedNumber)) {
            // 布隆过滤器说不存在，则一定不存在
            cacheResult(trimmedNumber, false);
            return false;
        }

        // 4. 布隆过滤器说可能存在，需要精确查询数据库
        boolean isSpam = queryDatabaseForNumber(trimmedNumber);

        // 5. 缓存结果到热点缓存
        cacheResult(trimmedNumber, isSpam);

        return isSpam;
    }

    /**
     * 分批查询骚扰号码数据
     */
    private List<SpamCallNumberInfo> getBatchSpamNumbers(int offset, int limit) {
        return spamCallNumberInfoMapper.getBatchActiveSpamNumbers(spamCallConfig.getDatabaseName(), offset, limit);
    }

    /**
     * 精确查询数据库中的号码
     */
    private boolean queryDatabaseForNumber(String phoneNumber) {
        try {
            return spamCallNumberInfoMapper.existsSpamNumber(spamCallConfig.getDatabaseName(), phoneNumber) > 0;
        } catch (Exception e) {
            log.error("查询骚扰号码数据库失败: {}", phoneNumber, e);
            return false;
        }
    }

    /**
     * 缓存查询结果到热点缓存
     */
    private void cacheResult(String phoneNumber, boolean isSpam) {
        int maxSize = spamCallConfig.getHotCacheMaxSize();

        // 限制热点缓存大小，避免内存溢出
        if (hotNumbersCache.size() >= maxSize) {
            // 简单的LRU策略：清理一半缓存
            Iterator<String> iterator = hotNumbersCache.keySet().iterator();
            int removeCount = maxSize / 2;
            while (iterator.hasNext() && removeCount > 0) {
                iterator.next();
                iterator.remove();
                removeCount--;
            }
        }

        hotNumbersCache.put(phoneNumber, isSpam);
    }
}
