package com.semptian.service;

import com.semptian.model.vo.AdsOpsOpsApiAccessResult;

import java.util.List;

public interface InterSystemDataProducerService {
    List<AdsOpsOpsApiAccessResult> collectMetrics(Integer metricId, String collectTimeRange);

    void produceSampleAccount(Integer maxSampleCount);

    /**
     * 根据采样配置及日期更新采样账号
     *
     * @param maxSampleCount 最大采样数量
     * @param sampleDate 采样日期，格式为yyyy-MM-dd
     * @return 更新结果
     */
    boolean updateSampleAccountByDate(Integer maxSampleCount, String sampleDate);
}
