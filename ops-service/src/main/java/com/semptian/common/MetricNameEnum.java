package com.semptian.common;

public enum MetricNameEnum {
    ACTIVE_COUNT("活跃次数", "ops.metric.name.active_count"),
    CONTACT_ACCOUNT_COUNT("通联账号及次数", "ops.metric.name.contact_account_count"),
    RADIUS_ACCOUNT_COUNT("关联RADIUS账号数量", "ops.metric.name.radius_account_count"),
    NETWORK_ACCESS_COUNT("上网次数", "ops.metric.name.network_access_count"),
    NETWORK_ACCESS_DETAIL("上网明细", "ops.metric.name.network_access_detail"),
    NETWORK_VIRTUAL_ACCOUNT("上网关联虚拟账号", "ops.metric.name.network_virtual_account"),
    COMMUNICATION_COUNT("通信次数", "ops.metric.name.communication_count"),
    CONTACT_ACCOUNT_DETAIL("通联账号明细", "ops.metric.name.contact_account_detail"),
    AUTH_BILLING("鉴权计费", "ops.metric.name.auth_billing"),
    BASE_STATION_LOCATION("基站位置", "ops.metric.name.base_station_location"),
    NETWORK_ACCESS_FREQUENCY("访问网络次数", "ops.metric.name.network_access_frequency"),
    VIRTUAL_ACCOUNT_COUNT("关联虚拟账号数量", "ops.metric.name.virtual_account_count"),
    AUTH_RECORD("认证记录", "ops.metric.name.auth_record"),

    // Monitor Types
    EMAIL_ANALYSIS("Email数据差异分析", "ops.monitor.type.email_analysis"),
    NUMBER_ANALYSIS("号码数据差异分析", "ops.monitor.type.number_analysis"),
    FIXED_RADIUS_ANALYSIS("Fixed RADIUS数据差异分析", "ops.monitor.type.fixed_radius_analysis"),
    IM_ANALYSIS("IM账号数据差异分析", "ops.monitor.type.im_analysis");

    private final String name;
    private final String i18nKey;

    MetricNameEnum(String name, String i18nKey) {
        this.name = name;
        this.i18nKey = i18nKey;
    }

    public String getName() {
        return name;
    }

    public String getI18nKey() {
        return i18nKey;
    }

    public static MetricNameEnum fromName(String name) {
        for (MetricNameEnum value : values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;
    }
}