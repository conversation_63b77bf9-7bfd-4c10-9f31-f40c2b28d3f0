package com.semptian.util;

import java.util.BitSet;

/**
 * 简单的布隆过滤器实现
 * 用于优化骚扰号码库的内存使用
 */
public class BloomFilter {
    
    private final BitSet bitSet;
    private final int bitSetSize;
    private final int hashFunctionCount;
    
    /**
     * 构造布隆过滤器
     * 
     * @param expectedInsertions 预期插入元素数量
     * @param fpp 误判率
     */
    public BloomFilter(int expectedInsertions, double fpp) {
        this.bitSetSize = optimalNumOfBits(expectedInsertions, fpp);
        this.hashFunctionCount = optimalNumOfHashFunctions(expectedInsertions, bitSetSize);
        this.bitSet = new BitSet(bitSetSize);
    }
    
    /**
     * 添加元素到布隆过滤器
     * 
     * @param element 要添加的元素
     */
    public void add(String element) {
        if (element == null) {
            return;
        }
        
        int[] hashes = getHashes(element);
        for (int hash : hashes) {
            bitSet.set(Math.abs(hash % bitSetSize));
        }
    }
    
    /**
     * 检查元素是否可能存在
     * 
     * @param element 要检查的元素
     * @return true表示可能存在，false表示一定不存在
     */
    public boolean mightContain(String element) {
        if (element == null) {
            return false;
        }
        
        int[] hashes = getHashes(element);
        for (int hash : hashes) {
            if (!bitSet.get(Math.abs(hash % bitSetSize))) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 清空布隆过滤器
     */
    public void clear() {
        bitSet.clear();
    }
    
    /**
     * 获取元素的多个哈希值
     */
    private int[] getHashes(String element) {
        int[] hashes = new int[hashFunctionCount];
        int hash1 = element.hashCode();
        int hash2 = hash1 >>> 16;
        
        for (int i = 0; i < hashFunctionCount; i++) {
            hashes[i] = hash1 + i * hash2;
        }
        return hashes;
    }
    
    /**
     * 计算最优的位数组大小
     */
    private static int optimalNumOfBits(long n, double p) {
        if (p == 0) {
            p = Double.MIN_VALUE;
        }
        return (int) (-n * Math.log(p) / (Math.log(2) * Math.log(2)));
    }
    
    /**
     * 计算最优的哈希函数数量
     */
    private static int optimalNumOfHashFunctions(long n, long m) {
        return Math.max(1, (int) Math.round((double) m / n * Math.log(2)));
    }
    
    /**
     * 获取内存使用情况（字节）
     */
    public long getMemoryUsage() {
        return bitSetSize / 8 + 64; // BitSet内存 + 对象开销
    }
}
