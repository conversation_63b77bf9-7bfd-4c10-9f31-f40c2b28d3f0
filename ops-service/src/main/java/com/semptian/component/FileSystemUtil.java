package com.semptian.component;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Comparator;

/**
 * 文件系统工具类
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Slf4j
@Component
public class FileSystemUtil {

    @Value("${detail.export.base.path:/data/detail_export}")
    private String basePath;

    @Value("${detail.export.disk.threshold:70}")
    private int diskThreshold;

    /**
     * 检查磁盘使用率
     *
     * @return true表示磁盘使用率正常，false表示超过阈值
     */
    public boolean checkDiskUsage() {
        try {
            File baseDir = new File(basePath);
            if (!baseDir.exists()) {
                baseDir.mkdirs();
            }

            long totalSpace = baseDir.getTotalSpace();
            long freeSpace = baseDir.getFreeSpace();
            long usedSpace = totalSpace - freeSpace;
            
            double usagePercent = (double) usedSpace / totalSpace * 100;
            
            log.info("[磁盘检查] 总空间: {}GB | 已用: {}GB | 使用率: {:.2f}% | 阈值: {}%", 
                    totalSpace / (1024 * 1024 * 1024),
                    usedSpace / (1024 * 1024 * 1024),
                    usagePercent,
                    diskThreshold);
            
            if (usagePercent > diskThreshold) {
                log.warn("[磁盘检查] 磁盘使用率超过阈值 | 当前: {:.2f}% | 阈值: {}%", usagePercent, diskThreshold);
                return false;
            }
            
            return true;
        } catch (Exception e) {
            log.error("[磁盘检查] 检查磁盘使用率失败", e);
            return false;
        }
    }

    /**
     * 清理过期文件
     * 删除7天前的文件目录和指定日期下相同指标的文件目录
     *
     * @param metricId 指标ID
     * @param targetDate 目标日期，清理该日期下的指标文件
     */
    public void cleanupFiles(Integer metricId, LocalDate targetDate) {
        try {
            // 清理7天前的文件
            cleanupExpiredFiles();

            // 清理指定日期下的指标文件
            cleanupMetricFiles(metricId, targetDate);

        } catch (Exception e) {
            log.error("[文件清理] 清理文件失败", e);
        }
    }

    /**
     * 清理指定日期下所有指标的文件（用于全量导出前的统一清理）
     * 只清理指定日期下的文件，不清理7天前的文件（避免重复清理）
     *
     * @param targetDate 目标日期
     */
    public void cleanupAllMetricFiles(LocalDate targetDate) {
        try {
            String dateStr = targetDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            Path dateDir = Paths.get(basePath, dateStr);

            if (Files.exists(dateDir)) {
                deleteDirectory(dateDir);
                log.info("[文件清理] 删除日期目录: {}", dateDir.toString());
            }
        } catch (Exception e) {
            log.error("[文件清理] 清理日期目录失败", e);
        }
    }

    /**
     * 清理7天前的文件目录（公共方法）
     */
    public void cleanupExpiredFiles() {
        try {
            File baseDir = new File(basePath);
            if (!baseDir.exists()) {
                return;
            }

            LocalDate sevenDaysAgo = LocalDate.now().minusDays(7);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            File[] dateDirs = baseDir.listFiles(File::isDirectory);
            if (dateDirs != null) {
                for (File dateDir : dateDirs) {
                    try {
                        LocalDate dirDate = LocalDate.parse(dateDir.getName(), formatter);
                        if (dirDate.isBefore(sevenDaysAgo)) {
                            deleteDirectory(dateDir.toPath());
                            log.info("[文件清理] 删除过期目录: {}", dateDir.getAbsolutePath());
                        }
                    } catch (Exception e) {
                        log.warn("[文件清理] 解析日期目录失败: {}", dateDir.getName());
                    }
                }
            }
        } catch (Exception e) {
            log.error("[文件清理] 清理过期文件失败", e);
        }
    }

    /**
     * 清理指定日期下的指标文件目录
     *
     * @param metricId 指标ID
     * @param targetDate 目标日期
     */
    private void cleanupMetricFiles(Integer metricId, LocalDate targetDate) {
        try {
            String dateStr = targetDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            Path targetDir = Paths.get(basePath, dateStr, metricId.toString());
            if (Files.exists(targetDir)) {
                deleteDirectory(targetDir);
                log.info("[文件清理] 删除指标目录: {}", targetDir.toString());
            }
        } catch (Exception e) {
            log.error("[文件清理] 清理指标文件失败", e);
        }
    }

    /**
     * 递归删除目录
     *
     * @param path 目录路径
     */
    private void deleteDirectory(Path path) throws IOException {
        if (Files.exists(path)) {
            Files.walk(path)
                    .sorted(Comparator.reverseOrder())
                    .map(Path::toFile)
                    .forEach(File::delete);
        }
    }

    /**
     * 创建导出文件路径
     *
     * @param metricId     指标ID
     * @param protocolType 协议类型
     * @param timestamp    时间戳
     * @param targetDate   目标日期，用于创建目录结构
     * @return 文件路径
     */
    public Path createExportFilePath(Integer metricId, String protocolType, long timestamp, LocalDate targetDate) {
        String dateStr = targetDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 目录结构：/YYYY-MM-DD/metric_id/搜索/ads/
        Path dirPath = Paths.get(basePath, dateStr, metricId.toString(), "搜索", "ads");

        try {
            Files.createDirectories(dirPath);
        } catch (IOException e) {
            log.error("[文件创建] 创建目录失败: {}", dirPath.toString(), e);
            throw new RuntimeException("创建导出目录失败", e);
        }

        // 文件命名：协议类型+时间戳.csv
        String fileName = protocolType + timestamp + ".csv";
        return dirPath.resolve(fileName);
    }
}
