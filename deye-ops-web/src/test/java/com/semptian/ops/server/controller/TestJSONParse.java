package com.semptian.ops.server.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.semptian.service.impl.parser.MetricKvDataDefaultParser;

public class TestJSONParse {
    public static void main(String[] args) {
//        MetricKvDataDefaultParser parser = new MetricKvDataDefaultParser();
//
//        String json = "{\n" +
//                "        \"doc_count\": 3,\n" +
//                "        \"key\":\"vqde\\`cyml)`ig?c\"\n" +
//                "    }";
//
//        String s = parser.preprocessJsonString(json, "测试", "测试", "测试");
//
//        JSONObject jsonObject = JSON.parseObject(s);
//        System.out.println(jsonObject);
//        jsonObject.keySet().forEach(key -> {
//            System.out.println(key);
//            System.out.println(jsonObject.get(key));
//        });

        String s = "{\\abc: 1}";
        System.out.println(s);
    }
}
