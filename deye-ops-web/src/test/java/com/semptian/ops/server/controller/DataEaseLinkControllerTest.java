package com.semptian.ops.server.controller;

import com.semptian.service.DataEaseLinkService;
import lombok.Data;
import lombok.Getter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.everyItem;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR>
 * @date 2025/3/16 下午12:27
 * @description dataease测试用例
 */
@SpringBootTest
@AutoConfigureMockMvc
class DataEaseLinkControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DataEaseLinkService dataEaseLinkService;

    private final Map<String, Object> mockData = new HashMap<>();

    private List<DataEaseLinkControllerTest.Link> mockLinks;


    /**
     * web项目上下文
     */
    @Autowired
    private WebApplicationContext webApplicationContext;


    @BeforeEach
    void setup() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        mockLinks = Arrays.asList(new Link("azNU7jg3", "DataCollectionAndAnalysis", "http://**************:8100/#/de-link/azNU7jg3", "data-collection-analysis"), new Link("MmG6SOrC", "DataConsistencyStatistics", "http://**************:8100/#/de-link/MmG6SOrC", "data-production-analysis"), new Link("FEdeRnMU", "DataLandingAndAnalysis", "http://**************:8100/#/de-link/FEdeRnMU", "data-collection-analysis"), new Link("YNx7vyp6", "NFDataProductionMonitoring", "http://**************:8100/#/de-link/YNx7vyp6", "data-production-analysis"), new Link("TItYdelR", "PhoneDataProductionMonitoring", "http://**************:8100/#/de-link/TItYdelR", "data-production-analysis"), new Link("DUS6NTBs", "RadiusDataProductionMonitoring", "http://**************:8100/#/de-link/DUS6NTBs", "data-production-analysis"), new Link("rFzMzc8g", "OverviewDataCollectionandAnalysis", "http://**************:8100/#/de-link/rFzMzc8g", "overview"), new Link("wOOeLvOR", "OverviewDataProductionandAnalysis", "http://**************:8100/#/de-link/wOOeLvOR", "overview"));
    }

    //测试用例 数据采集分析模块
    @Test
    void should_filter_links_by_data_collection_analysis_module() throws Exception {
        when(dataEaseLinkService.getLinks("data-collection-analysis")).thenReturn(mockLinks.stream().filter(l -> l.getModule().equals("data-collection-analysis")).collect(Collectors.toList()));

        mockMvc.perform(MockMvcRequestBuilders.get("/dataease/api/links.json").param("module_type", "data-collection-analysis")).andExpect(status().isOk()).andExpect(jsonPath("$.data[0].id").value("azNU7jg3")).andExpect(jsonPath("$.data[0].name").value("DataCollectionAndAnalysis")).andExpect(jsonPath("$.data.size()").value(2));
    }

    //测试用例 数据生产分析模块
    @Test
    void should_filter_links_by_data_production_analysis_module() throws Exception {
        when(dataEaseLinkService.getLinks("data-production-analysis")).thenReturn(mockLinks.stream().filter(l -> l.getModule().equals("data-production-analysis")).collect(Collectors.toList()));

        mockMvc.perform(MockMvcRequestBuilders.get("/dataease/api/links.json").param("module_type", "data-production-analysis")).andExpect(status().isOk()).andExpect(jsonPath("$.data[0].url").value("http://**************:8100/#/de-link/MmG6SOrC")).andExpect(jsonPath("$.data.size()").value(4));
    }

    //测试用例 概览模块
    @Test
    void should_return_overview_module_links() throws Exception {
        when(dataEaseLinkService.getLinks("overview")).thenReturn(mockLinks.stream().filter(l -> l.getModule().equals("overview")).collect(Collectors.toList()));

        mockMvc.perform(MockMvcRequestBuilders.get("/dataease/api/links.json").param("module_type", "overview")).andExpect(status().isOk()).andExpect(jsonPath("$.data[*].module").value(everyItem(equalTo("overview")))).andExpect(jsonPath("$.data.size()").value(2));
    }

    @Data
    private static class Link {
        private String id;
        private String name;
        private String url;
        @Getter
        private String module;

        public Link(String id, String name, String url, String module) {
            this.id = id;
            this.name = name;
            this.url = url;
            this.module = module;
        }
    }
}