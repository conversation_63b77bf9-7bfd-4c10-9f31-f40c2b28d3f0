package com.semptian.ops.server.controller;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.semptian.entity.MenuEntity;
import com.semptian.service.ApplicationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static reactor.core.publisher.Mono.when;

@SpringBootTest
@AutoConfigureMockMvc
public class ApplicationControllerTest {
    @Autowired
    private MockMvc mvc;

    @Autowired
    private WebApplicationContext webApplicationContext;

    @MockBean
    private ApplicationService applicationService;

    Map<String, List> map = Maps.newHashMap();

    List<MenuEntity> list = Lists.newArrayList();

    @BeforeEach
    void setup() {
        mvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        MenuEntity menu1 = new MenuEntity();
        menu1.setId("me1");
        menu1.setMenuName("overview");
        menu1.setMenuIcon(null);
        menu1.setMenuCode("overview");
        menu1.setParentMenuCode("0");
        menu1.setMenuUrl("");
        menu1.setIsHide(0);
        menu1.setMenuDesc("");
        menu1.setPid("0");
        menu1.setAppId(70);
        menu1.setAppName(null);
        menu1.setDelStatus(0);
        menu1.setCreateTime(1740623928655L);
        menu1.setCreateUser("-1");
        menu1.setModifyTime(null);
        menu1.setModifyUser(null);
        menu1.setPathName(null);
        menu1.setIsLast(0);
        menu1.setOrder(467);
        list.add(menu1);

        MenuEntity menu2 = new MenuEntity();
        menu2.setId("me2");
        menu2.setMenuName("alarm-query");
        menu2.setMenuIcon(null);
        menu2.setMenuCode("alarm-query");
        menu2.setParentMenuCode("0");
        menu2.setMenuUrl("");
        menu2.setIsHide(0);
        menu2.setMenuDesc("");
        menu2.setPid("0");
        menu2.setAppId(70);
        menu2.setAppName(null);
        menu2.setDelStatus(0);
        menu2.setCreateTime(1740623928655L);
        menu2.setCreateUser("-1");
        menu2.setModifyTime(null);
        menu2.setModifyUser(null);
        menu2.setPathName(null);
        menu2.setIsLast(0);
        menu2.setOrder(467);
        list.add(menu2);

        MenuEntity menu3 = new MenuEntity();
        menu3.setId("me3");
        menu3.setMenuName("real-time-statistics");
        menu3.setMenuIcon(null);
        menu3.setMenuCode("real-time-statistics");
        menu3.setParentMenuCode("0");
        menu3.setMenuUrl("");
        menu3.setIsHide(0);
        menu3.setMenuDesc("");
        menu3.setPid("0");
        menu3.setAppId(70);
        menu3.setAppName(null);
        menu3.setDelStatus(0);
        menu3.setCreateTime(1740623928655L);
        menu3.setCreateUser("-1");
        menu3.setModifyTime(null);
        menu3.setModifyUser(null);
        menu3.setPathName(null);
        menu3.setIsLast(0);
        menu3.setOrder(467);
        list.add(menu3);

        MenuEntity menu4 = new MenuEntity();
        menu4.setId("me4");
        menu4.setMenuName("data-monitor");
        menu4.setMenuIcon(null);
        menu4.setMenuCode("data-monitor");
        menu4.setParentMenuCode("0");
        menu4.setMenuUrl("");
        menu4.setIsHide(0);
        menu4.setMenuDesc("");
        menu4.setPid("0");
        menu4.setAppId(70);
        menu4.setAppName(null);
        menu4.setDelStatus(0);
        menu4.setCreateTime(1740623928655L);
        menu4.setCreateUser("-1");
        menu4.setModifyTime(null);
        menu4.setModifyUser(null);
        menu4.setPathName(null);
        menu4.setIsLast(0);
        menu4.setOrder(467);
        list.add(menu4);

        MenuEntity menu5 = new MenuEntity();
        menu5.setId("me5");
        menu5.setMenuName("data-collection-analysis");
        menu5.setMenuIcon(null);
        menu5.setMenuCode("data-collection-analysis");
        menu5.setParentMenuCode("data-monitor");
        menu5.setMenuUrl("");
        menu5.setIsHide(0);
        menu5.setMenuDesc("");
        menu5.setPid("me4");
        menu5.setAppId(70);
        menu5.setAppName(null);
        menu5.setDelStatus(0);
        menu5.setCreateTime(1740623928655L);
        menu5.setCreateUser("-1");
        menu5.setModifyTime(null);
        menu5.setModifyUser(null);
        menu5.setPathName(null);
        menu5.setIsLast(0);
        menu5.setOrder(467);
        list.add(menu5);

        MenuEntity menu6 = new MenuEntity();
        menu6.setId("me5");
        menu6.setMenuName("data-production-analysis");
        menu6.setMenuIcon(null);
        menu6.setMenuCode("data-production-analysis");
        menu6.setParentMenuCode("data-monitor");
        menu6.setMenuUrl("");
        menu6.setIsHide(0);
        menu6.setMenuDesc("");
        menu6.setPid("me4");
        menu6.setAppId(70);
        menu6.setAppName(null);
        menu6.setDelStatus(0);
        menu6.setCreateTime(1740623928655L);
        menu6.setCreateUser("-1");
        menu6.setModifyTime(null);
        menu6.setModifyUser(null);
        menu6.setPathName(null);
        menu6.setIsLast(0);
        menu6.setOrder(467);
        list.add(menu6);

        MenuEntity menu7 = new MenuEntity();
        menu7.setId("me6");
        menu7.setMenuName("business-data-analysis");
        menu7.setMenuIcon(null);
        menu7.setMenuCode("business-data-analysis");
        menu7.setParentMenuCode("data-monitor");
        menu7.setMenuUrl("");
        menu7.setIsHide(0);
        menu7.setMenuDesc("");
        menu7.setPid("me4");
        menu7.setAppId(70);
        menu7.setAppName(null);
        menu7.setDelStatus(0);
        menu7.setCreateTime(1740623928655L);
        menu7.setCreateUser("-1");
        menu7.setModifyTime(null);
        menu7.setModifyUser(null);
        menu7.setPathName(null);
        menu7.setIsLast(0);
        menu7.setOrder(467);
        list.add(menu7);

        map.put("menuList", list);
        map.put("operateList", Lists.newArrayList());
        map.put("permissionTreeList", Lists.newArrayList());

    }

    // Service的返回值是Map,放到when方法里报错 待解决
    //http://192.168.80.188:6062/ops/application/get_menu_operate_by_appid.json?lang=zh_CN&appId=70&allPermission=true
    /*@Test
    void getMenuAndOperateByAppid() throws Exception {

        when(applicationService.getMenuAndOperateByAppid("", new String[] {}, "70", "1", true, true))
                .thenReturn(map);

        mvc.perform(get("/application/get_menu_operate_by_appid.json?appId=1")
                .header("userId", "1"));


    }*/

}
