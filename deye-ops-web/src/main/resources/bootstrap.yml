server:
  port: ${SERVER-PORT:8999}
  servlet:
    context-path: /ops

spring:
  profiles:
    active: dev
  application:
    name: deye-ops-web
  messages:
    basename: i18n.messages
  servlet:
    multipart:
      # 单个文件最大大小（支持1GB文件上传）
      max-file-size: 1GB
      # 整个请求最大大小
      max-request-size: 1GB
      # 文件写入磁盘的阈值
      file-size-threshold: 10MB
      # 临时文件存储位置
      location: /tmp
  redis:
    cache-prefix: ops-web
    cluster:
      nodes: *************:7001,*************:7002,*************:7003,*************:7004,*************:7005,*************:7006
      max-redirects: 3
    password: pico-nf-8100
    timeout: 3000

  datasource:
    dynamic:
      datasource:
        doris:
          driver-class-name: com.mysql.jdbc.Driver
          password: 123456
          type: com.alibaba.druid.pool.DruidDataSource
          url: jdbc:mysql://************:9030/ads_ops?useServerPrepStmts=true&sessionVariables=group_commit=async_mode
          username: root
        archive-doris:
          driver-class-name: com.mysql.jdbc.Driver
          password: 123456
          type: com.alibaba.druid.pool.DruidDataSource
          url: ************************************************************************************************************
          username: root
        mysql:
          driver-class-name: com.mysql.jdbc.Driver
          password: 123456
          type: com.alibaba.druid.pool.DruidDataSource
          url: **********************************************************
          username: root
      druid:
        filters: stat
        initial-size: 3
        max-active: 20
        max-wait: 5000
        min-idle: 3
      primary: mysql
      strict: false
inter_system:
  max_sample_size: 2000
  case_thread_num: 2
  archive_thread_num: 4
  search_thread_num: 8
  archive_id_url: http://**************:8109/archives_web/arc_info/arc_info.json
  #是否使用mock数据，严禁在线上置为true，仅在本地生产mock数据时才能打开！！！ 严禁提交true
  is_use_mock: false
  # 请求延迟时间（毫秒），用于控制请求频率，降低系统压力
  request_delay: 100
doris:
  host: ************:8030
  db: ads_ops
  user: root
  password: 123456
  table: ops_api_access_result
  result_difference_table: ops_api_access_result_difference

elasticsearch:
  host: *************
  port: 12000
  timeout: 30
  disable_warnings: true  # 设置为true禁用Elasticsearch安全警告

# 骚扰号码库配置
spam-call:
  database-name: deye_basic_library_v6.4.3

# 明细导出导入功能配置
detail:
  export:
    # ES查询优化配置
    es:
      # 批次大小：每次ES查询处理的账号数量（批量查询模式）
      batch:
        size: 50
      # 并发大小：单账号查询模式下的并发数量
      concurrent:
        size: 5
    base:
      path: /data/detail_export
    disk:
      threshold: 70
  import:
    # 默认Doris表名前缀（按协议分表）
    default:
      table:
        prefix: ops_detail_
    # 批次大小：每次导入到Doris的记录数
    batch:
      size: 1000
    # 流式处理批次大小：大文件流式处理时每批处理的记录数（减小批次以降低内存使用）
    stream:
      batch:
        size: 5000
    # 临时文件存储路径
    temp:
      path: /tmp/detail_import
    # 最大文件大小（字节）2GB
    max:
      file:
        size: 2147483648
    # 协议表映射配置
    protocol:
      tables:
        call: ops_detail_call
        email: ops_detail_email
        im: ops_detail_im
        http: ops_detail_http
        ftp: ops_detail_ftp
        finance: ops_detail_finance
        entertainment: ops_detail_entertainment
        multimedia: ops_detail_multimedia
        engine: ops_detail_engine
        lbs: ops_detail_lbs
        location: ops_detail_location
        fax: ops_detail_fax
        fixednetradius: ops_detail_fixednetradius
        mobilenetradius: ops_detail_mobilenetradius

management:
  health:
    elasticsearch:
      enabled: false

# HBase配置
hbase:
  zookeeper:
    quorum: 172.16.80.11,172.16.80.12,172.16.80.13,172.16.80.14,************,************
    port: 2181
    znode:
      parent: /hbase-unsecure
  # 每批次最大查询条数，默认1000
  batch:
    size: 1000
  client:
    timeout: 60000
    retries:
      number: 3
  rpc:
    timeout: 60000

# 明细导出配置

# Redis锁配置
redis:
  lock:
    enable: true

# 定时任务配置
scheduled:
  tasks:
    # 每日指标差异分析和明细导出任务
    daily:
      # 每天上午12点执行
      cron: "0 0 12 * * ?"
      enabled: true
      description: "每天上午12点自动执行：1.获取昨日所有指标明细差异 2.导出昨日所有指标HBase明细数据"

