-- 运维监控系统
create workload group if not exists g_ops properties ("cpu_share"="111","memory_limit"="10%","enable_memory_overcommit"="true");
-- 创建ops_Y用户
CREATE USER 'ops_Y'@'%' IDENTIFIED BY '123456';
-- 给ops_Y用户赋予具体库的增删改查权限
GRANT SELECT_PRIV,LOAD_PRIV,ALTER_PRIV,CREATE_PRIV,DROP_PRIV on ads_ops.* TO 'ops_Y'@'%';
-- 给ops_Y用户赋予WORKLOAD组
GRANT USAGE_PRIV ON WORKLOAD GROUP 'g_ops' TO 'ops_Y'@'%';
-- 创建ops_Z用户
CREATE USER 'ops_Z'@'%' IDENTIFIED BY '123456';
-- 给ops_Z用户赋予具体库的增删改查权限
GRANT SELECT_PRIV,LOAD_PRIV,ALTER_PRIV,CREATE_PRIV,DROP_PRIV on ads_ops.* TO 'ops_Z'@'%';
-- 给ops_Z用户赋予WORKLOAD组
GRANT USAGE_PRIV ON WORKLOAD GROUP 'g_ops' TO 'ops_Z'@'%';