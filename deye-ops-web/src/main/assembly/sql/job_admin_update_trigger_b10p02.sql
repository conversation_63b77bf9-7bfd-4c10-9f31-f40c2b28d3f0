INSERT INTO `JOB_QRTZ_TRIGGER_INFO` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `job_type`, `el_job_id`, `job_trigger_type`, `job_start_time`, `job_expired_time`, `executor_fail_retry_interval`, `source_app_name`) VALUES (2, '0 0 9 * * ?', '系统间-一致性统计结果差异计算', '2025-05-22 03:46:52', '2025-05-22 11:52:04', 'deye-ops', '', 'FIRST', 'httpJobHandler', 'http://192.168.80.189:8999/ops/metrics/producer/compare_result.json?timeRange=2025-05-09,2025-05-09', 'SERIAL_EXECUTION', 3600, 0, 'BEAN', '', 'GLUE代码初始化', '2025-05-22 03:46:52', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `JOB_QRTZ_TRIGGER_INFO` (`job_group`, `job_cron`, `job_desc`, `add_time`, `update_time`, `author`, `alarm_email`, `executor_route_strategy`, `executor_handler`, `executor_param`, `executor_block_strategy`, `executor_timeout`, `executor_fail_retry_count`, `glue_type`, `glue_source`, `glue_remark`, `glue_updatetime`, `child_jobid`, `job_type`, `el_job_id`, `job_trigger_type`, `job_start_time`, `job_expired_time`, `executor_fail_retry_interval`, `source_app_name`) VALUES (2, '0 0 5 * * ?', '系统间-动态号码历史采样更新', '2025-05-16 09:55:01', '2025-05-16 09:55:01', 'deye-ops', '', 'FIRST', 'httpJobHandler', 'http://192.168.80.189:8999/ops/metrics/producer/update_by_date.json?maxSampleCount=200&sampleDate=2025-05-09', 'SERIAL_EXECUTION', 3600, 0, 'BEAN', '', 'GLUE代码初始化', '2025-05-16 09:55:01', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL);