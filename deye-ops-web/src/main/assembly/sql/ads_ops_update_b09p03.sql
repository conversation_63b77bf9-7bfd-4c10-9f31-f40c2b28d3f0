-- ads_ops.ops_nf_radius_association_detail definition
DROP TABLE IF EXISTS `ops_nf_radius_association_detail`;
CREATE TABLE IF NOT EXISTS ops_nf_radius_association_detail(
    `capture_day` DATE  NOT NULL  COMMENT '数据日期',
    `operator` VARCHAR(10)  NOT NULL COMMENT '运营商;<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>',
    `statistics_type` VARCHAR(20)  NOT NULL COMMENT '统计类型(Association/Accuracy,_1为账号异常,_2为时间异常)',
    `auth_type` VARCHAR(7) NOT NULL COMMENT  '认证类型(Mobile/Fixed)',
    `radius_ip` VARCHAR(64) COMMENT 'RADIUS IP',
    `data_type` VARCHAR(20) NOT NULL COMMENT  '数据类型',
    `radius_account` VARCHAR(128) COMMENT 'RADIUS账号',
    `radius_start_port` INT COMMENT '起始端口',
    `radius_end_port` INT COMMENT '结束端口',
    `radius_start_time` BIGINT COMMENT '会话开始时间',
    `radius_end_time` BIGINT COMMENT '会话结束时间',
    `account` VARCHAR(128) COMMENT '账号',
    `strsrc_ip` VARCHAR(64) COMMENT '源IP',
    `src_port` INT COMMENT '源端口',
    `capture_time` BIGINT NOT NULL COMMENT '捕获时间',
    `data_id` VARCHAR(64) COMMENT '数据ID',
    `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
DUPLICATE KEY(capture_day, operator, statistics_type, auth_type, radius_ip,data_type)
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(radius_ip) BUCKETS 3
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********"
);

-- ads_ops.ops_pr_radius_association_detail definition
DROP TABLE IF EXISTS `ops_pr_radius_association_detail`;
CREATE TABLE IF NOT EXISTS ops_pr_radius_association_detail(
    `capture_day` DATE  NOT NULL  COMMENT '数据日期',
    `operator` VARCHAR(10)  NOT NULL COMMENT '运营商;Ooredoo,Mobilis,Djezzy,Autre',
    `statistics_type` VARCHAR(20)  NOT NULL COMMENT '统计类型(Association/Accuracy,_1为账号异常,_2为时间异常)',
    `auth_type` VARCHAR(7) NOT NULL COMMENT  '认证类型(Mobile/Fixed)',
    `radius_ip` VARCHAR(64) COMMENT 'RADIUS IP',
    `data_type` VARCHAR(20) NOT NULL COMMENT  '数据类型',
    `radius_account` VARCHAR(128) COMMENT 'RADIUS账号',
    `radius_start_port` INT COMMENT '起始端口',
    `radius_end_port` INT COMMENT '结束端口',
    `radius_start_time` BIGINT COMMENT '会话开始时间',
    `radius_end_time` BIGINT COMMENT '会话结束时间',
    `account` VARCHAR(128) COMMENT '账号',
    `strsrc_ip` VARCHAR(64) COMMENT '源IP',
    `src_port` INT COMMENT '源端口',
    `capture_time` BIGINT NOT NULL COMMENT '捕获时间',
    `data_id` VARCHAR(64) COMMENT '数据ID',
    `create_time` bigint NOT NULL COMMENT '创建时间'
) ENGINE=OLAP
DUPLICATE KEY(capture_day, operator, statistics_type, auth_type, radius_ip,data_type)
AUTO PARTITION BY RANGE (date_trunc(`capture_day`, 'day'))
()
DISTRIBUTED BY HASH(radius_ip) BUCKETS 3
PROPERTIES (
"replication_allocation" = "tag.location.default: 3",
"min_load_replica_num" = "-1",
"is_being_synced" = "false",
"storage_medium" = "hdd",
"storage_format" = "V2",
"inverted_index_storage_format" = "V1",
"light_schema_change" = "true",
"disable_auto_compaction" = "false",
"enable_single_replica_compaction" = "false",
"group_commit_interval_ms" = "10000",
"group_commit_data_bytes" = "*********"
);