-- 修复后的Doris协议分表结构（去掉AUTO_INCREMENT）
-- 注意：这些表需要在Doris数据库中创建，不是在MySQL中

-- 通话协议明细表
DROP TABLE IF EXISTS `ops_detail_call`;
CREATE TABLE `ops_detail_call` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `called_number` VARCHAR(128) COMMENT '被叫号码',
  `calling_number` VARCHAR(128) COMMENT '主叫号码',
  `call_type_map` VARCHAR(64) COMMENT '通话类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `duration` VARCHAR(32) COMMENT '通话时长',
  `end_time` VARCHAR(32) COMMENT '结束时间',
  `start_time` VARCHAR(32) COMMENT '开始时间',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 邮件协议明细表
DROP TABLE IF EXISTS `ops_detail_email`;
CREATE TABLE `ops_detail_email` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `mail_bcc` TEXT COMMENT '邮件密送',
  `mail_cc` TEXT COMMENT '邮件抄送',
  `mail_date` VARCHAR(32) COMMENT '邮件日期',
  `mail_from` VARCHAR(256) COMMENT '发件人',
  `mail_to` TEXT COMMENT '收件人',
  `spam_flag` VARCHAR(32) COMMENT '垃圾邮件标识',
  `sub_protocol_map` VARCHAR(64) COMMENT '子协议映射',
  `username` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 搜索引擎协议明细表
DROP TABLE IF EXISTS `ops_detail_engine`;
CREATE TABLE `ops_detail_engine` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 娱乐协议明细表
DROP TABLE IF EXISTS `ops_detail_entertainment`;
CREATE TABLE `ops_detail_entertainment` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `username` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 传真协议明细表
DROP TABLE IF EXISTS `ops_detail_fax`;
CREATE TABLE `ops_detail_fax` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `called_number` VARCHAR(128) COMMENT '被叫号码',
  `calling_number` VARCHAR(128) COMMENT '主叫号码',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `end_time` VARCHAR(32) COMMENT '结束时间',
  `start_time` VARCHAR(32) COMMENT '开始时间',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 金融协议明细表
DROP TABLE IF EXISTS `ops_detail_finance`;
CREATE TABLE `ops_detail_finance` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `username` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 固网RADIUS协议明细表
DROP TABLE IF EXISTS `ops_detail_fixednetradius`;
CREATE TABLE `ops_detail_fixednetradius` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `login_time` VARCHAR(32) COMMENT '登录时间',
  `logout_time` VARCHAR(32) COMMENT '登出时间',
  `strsrc_ip` VARCHAR(64) COMMENT '源IP地址',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- FTP协议明细表
DROP TABLE IF EXISTS `ops_detail_ftp`;
CREATE TABLE `ops_detail_ftp` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `username` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- HTTP协议明细表
DROP TABLE IF EXISTS `ops_detail_http`;
CREATE TABLE `ops_detail_http` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `sub_protocol_map` VARCHAR(64) COMMENT '子协议映射',
  `username` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- IM协议明细表
DROP TABLE IF EXISTS `ops_detail_im`;
CREATE TABLE `ops_detail_im` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `from_id` VARCHAR(256) COMMENT '发送方ID',
  `from_nickname` VARCHAR(256) COMMENT '发送方昵称',
  `to_id` VARCHAR(256) COMMENT '接收方ID',
  `to_nickname` VARCHAR(256) COMMENT '接收方昵称',
  `username` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- LBS协议明细表
DROP TABLE IF EXISTS `ops_detail_lbs`;
CREATE TABLE `ops_detail_lbs` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `base_station_id` VARCHAR(128) COMMENT '基站ID',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `position` VARCHAR(512) COMMENT '位置信息',
  `username` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 定位协议明细表
DROP TABLE IF EXISTS `ops_detail_location`;
CREATE TABLE `ops_detail_location` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `base_station_address` VARCHAR(512) COMMENT '基站地址',
  `base_station_location` VARCHAR(512) COMMENT '基站位置',
  `base_station_number` VARCHAR(128) COMMENT '基站号码',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `strsrc_ip` VARCHAR(64) COMMENT '源IP地址',
  `user_name` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 移动网RADIUS协议明细表
DROP TABLE IF EXISTS `ops_detail_mobilenetradius`;
CREATE TABLE `ops_detail_mobilenetradius` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `called_station_id` VARCHAR(128) COMMENT '被叫站点ID',
  `calling_station_id` VARCHAR(128) COMMENT '主叫站点ID',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `login_time` VARCHAR(32) COMMENT '登录时间',
  `logout_time` VARCHAR(32) COMMENT '登出时间',
  `strsrc_ip` VARCHAR(64) COMMENT '源IP地址',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 多媒体协议明细表
DROP TABLE IF EXISTS `ops_detail_multimedia`;
CREATE TABLE `ops_detail_multimedia` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `username` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 新闻协议明细表
DROP TABLE IF EXISTS `ops_detail_news`;
CREATE TABLE `ops_detail_news` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `username` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 其他协议明细表
DROP TABLE IF EXISTS `ops_detail_other`;
CREATE TABLE `ops_detail_other` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `username` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 远程控制协议明细表
DROP TABLE IF EXISTS `ops_detail_remotectrl`;
CREATE TABLE `ops_detail_remotectrl` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `username` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 购物协议明细表
DROP TABLE IF EXISTS `ops_detail_shopping`;
CREATE TABLE `ops_detail_shopping` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 短信协议明细表
DROP TABLE IF EXISTS `ops_detail_sms`;
CREATE TABLE `ops_detail_sms` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `called_number` VARCHAR(128) COMMENT '被叫号码',
  `calling_number` VARCHAR(128) COMMENT '主叫号码',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 社交网络协议明细表
DROP TABLE IF EXISTS `ops_detail_sns`;
CREATE TABLE `ops_detail_sns` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `username` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- Telnet协议明细表
DROP TABLE IF EXISTS `ops_detail_telnet`;
CREATE TABLE `ops_detail_telnet` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `username` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 终端协议明细表
DROP TABLE IF EXISTS `ops_detail_terminal`;
CREATE TABLE `ops_detail_terminal` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 工具协议明细表
DROP TABLE IF EXISTS `ops_detail_tool`;
CREATE TABLE `ops_detail_tool` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `username` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- 出行协议明细表
DROP TABLE IF EXISTS `ops_detail_travel`;
CREATE TABLE `ops_detail_travel` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `username` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- VoIP协议明细表
DROP TABLE IF EXISTS `ops_detail_voip`;
CREATE TABLE `ops_detail_voip` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `username` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

-- VPN协议明细表
DROP TABLE IF EXISTS `ops_detail_vpn`;
CREATE TABLE `ops_detail_vpn` (
  `metric_id` INT COMMENT '指标ID',
  `system_name` VARCHAR(64) COMMENT '系统名称',
  `data_level` VARCHAR(64) COMMENT '数据级别',
  `table_name` VARCHAR(128) COMMENT '表名',
  `protocol_type` VARCHAR(64) COMMENT '协议类型',
  `account` VARCHAR(256) COMMENT '账号',
  `action_map` VARCHAR(128) COMMENT '动作映射',
  `auth_account` VARCHAR(256) COMMENT '认证账号',
  `auth_type_map` VARCHAR(64) COMMENT '认证类型映射',
  `capture_day` VARCHAR(32) COMMENT '采集日期',
  `capture_time` VARCHAR(32) COMMENT '采集时间',
  `child_type_map` VARCHAR(256) COMMENT '子类型映射',
  `data_id` VARCHAR(128) COMMENT '数据ID',
  `data_type_map` VARCHAR(64) COMMENT '数据类型映射',
  `username` VARCHAR(256) COMMENT '用户名',
  `import_time` BIGINT COMMENT '导入时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=OLAP DUPLICATE KEY(`metric_id`) DISTRIBUTED BY HASH(`metric_id`) BUCKETS 3 PROPERTIES ("replication_allocation" = "tag.location.default: 1");

