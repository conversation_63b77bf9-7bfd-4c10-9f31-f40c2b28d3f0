-- 明细导入功能初始化SQL脚本

-- 创建明细导入任务表
CREATE TABLE IF NOT EXISTS `tb_ops_detail_import_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `task_id` varchar(64) NOT NULL COMMENT '任务ID',
  `metric_id` int(11) DEFAULT NULL COMMENT '指标ID',
  `target_date` date DEFAULT NULL COMMENT '目标日期',
  `table_name` varchar(128) NOT NULL COMMENT '目标Doris表名',
  `file_path` varchar(512) DEFAULT NULL COMMENT '文件路径',
  `file_name` varchar(256) DEFAULT NULL COMMENT '文件名',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小（字节）',
  `total_rows` bigint(20) DEFAULT 0 COMMENT '总行数',
  `processed_rows` bigint(20) DEFAULT 0 COMMENT '已处理行数',
  `success_rows` bigint(20) DEFAULT 0 COMMENT '成功行数',
  `failed_rows` bigint(20) DEFAULT 0 COMMENT '失败行数',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '任务状态:0=待处理,1=处理中,2=成功,3=失败,4=部分成功',
  `error_message` text COMMENT '错误信息',
  `start_time` bigint(20) DEFAULT NULL COMMENT '开始时间时间戳',
  `end_time` bigint(20) DEFAULT NULL COMMENT '结束时间时间戳',
  `create_time` bigint(20) DEFAULT NULL COMMENT '创建时间时间戳',
  `modify_time` bigint(20) DEFAULT NULL COMMENT '修改时间时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_metric_date` (`metric_id`, `target_date`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='明细导入任务表';