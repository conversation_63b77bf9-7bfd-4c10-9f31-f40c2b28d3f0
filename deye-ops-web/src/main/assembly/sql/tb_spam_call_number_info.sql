-- deye_ops.tb_spam_call_number_info definition
DROP TABLE IF EXISTS `tb_spam_call_number_info`;
CREATE TABLE `tb_spam_call_number_info` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `phone_number` varchar(128) NOT NULL COMMENT '号码,包含国家码,多个用逗号拼接,最多5个号码',
                                            `name` varchar(64) DEFAULT NULL COMMENT '名称',
                                            `type` tinyint(4) DEFAULT NULL COMMENT '号码类型:1=服务商/公司公用电话,2=诈骗电话,3=广告推销电话,99=其它',
                                            `remark` text COMMENT '备注',
                                            `is_del` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除状态,0=未删除,1=已删除,默认为0',
                                            `create_time` bigint(20) NOT NULL COMMENT '创建时间',
                                            `modify_time` bigint(20) NOT NULL COMMENT '更新时间',
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12211 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='骚扰号码信息库表';