#!/bin/bash
source /etc/profile
cd `dirname $0`
BIN_DIR=`pwd`
cd ..
DEPLOY_DIR=`pwd`
CONF_DIR=$DEPLOY_DIR/conf

SERVER_NAME="deye-ops-web"
PIDS=`ps -ef | grep java | grep "$CONF_DIR" | grep -v grep |awk '{print $2}'`

if [ -z "$PIDS" ]; then
    echo "ERROR: The $SERVER_NAME does not started!"
    exit 1
fi

echo -e "Stopping the $SERVER_NAME ...\c"
for PID in $PIDS ; do
    kill -9 $PID > /dev/null 2>&1
done

# 等待进程退出,最多等待30秒
TIMEOUT=30
while [ $TIMEOUT -gt 0 ]; do
    for PID in $PIDS ; do
        PID_EXIST=`ps -p $PID | grep -v "PID TTY" | grep -v grep`
        if [ -n "$PID_EXIST" ]; then
            # 如果进程还在,继续等待
            echo -n "."
            sleep 1
            let TIMEOUT=TIMEOUT-1
            continue 2
        fi
    done
    # 所有进程都已退出
    echo ""
    echo "Server stopped: $SERVER_NAME"
    echo "PID: $PIDS"
    exit 0
done

# 如果超时还没退出,强制杀死
echo ""
echo "Force killing process!"
for PID in $PIDS ; do
    kill -9 $PID > /dev/null 2>&1
done
echo "Server stopped by force: $SERVER_NAME"
echo "PID: $PIDS"