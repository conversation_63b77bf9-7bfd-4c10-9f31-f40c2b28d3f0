package com.semptian.ops.server.controller;

import com.semptian.base.service.ReturnModel;
import com.semptian.enums.ModuleNameEnum;
import com.semptian.enums.TaskStatusEnum;
import com.semptian.enums.TimeModeEnum;
import com.semptian.model.dto.TaskQueryDTO;
import com.semptian.service.TaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@Api(value = "Task API")
@RestController
@CrossOrigin
@RequestMapping("/real_time_tasks")
public class RealTimeTaskController {

    @Resource
    private TaskService taskService;

    @RequestMapping(value = "/list.json", method = RequestMethod.GET)
    @ApiOperation(value = "获取任务列表", notes = "根据模块类型过滤任务列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "page", value = "页码", dataType = "int", paramType = "query"), @ApiImplicitParam(name = "size", value = "每页数量", dataType = "int", paramType = "query"), @ApiImplicitParam(name = "keyword", value = "关键字", dataType = "string", paramType = "query"), @ApiImplicitParam(name = "sort", value = "排序字段", dataType = "string", paramType = "query"), @ApiImplicitParam(name = "desc", value = "是否降序", dataType = "boolean", paramType = "query"), @ApiImplicitParam(name = "module", value = "模块", dataType = "string", paramType = "query"), @ApiImplicitParam(name = "timeMode", value = "时间模式", dataType = "string", paramType = "query"), @ApiImplicitParam(name = "status", value = "任务状态", dataType = "string", paramType = "query")})
    public Object getTasks(@RequestParam(required = false) Integer page, @RequestParam(required = false) String keyword, @RequestParam(required = false) String sort, @RequestParam(required = false) boolean desc, @RequestParam(required = false) Integer size, @RequestParam(required = false) ModuleNameEnum module, @RequestParam(required = false) TimeModeEnum timeMode, @RequestParam(required = false) TaskStatusEnum status) {
        return ReturnModel.getInstance().ok(taskService.getTasksByPage(TaskQueryDTO.builder().page(page).size(size).keyword(keyword).sort(sort).desc(desc).module(module).timeMode(timeMode).status(status).build()));
    }

    @RequestMapping(value = "/execute.json", method = RequestMethod.POST)
    @ApiOperation(value = "执行任务", notes = "根据任务ID执行任务")
    @ApiImplicitParams({@ApiImplicitParam(name = "taskId", value = "任务ID", required = true, dataType = "string", paramType = "query", example = "1"), @ApiImplicitParam(name = "timeMode", value = "时间模式,DAY或者HOUR", dataType = "string", paramType = "query"), @ApiImplicitParam(name = "executeRange", value = "执行范围例如 2025-03-01~2025-03-07", dataType = "string", paramType = "query")})
    public Object executeTask(@RequestParam String taskId, @RequestParam(required = false) TimeModeEnum timeMode, @RequestParam(required = false) String executeRange) {
        return ReturnModel.getInstance().ok((taskService.executeTasks(taskId, timeMode, executeRange)));
    }
}