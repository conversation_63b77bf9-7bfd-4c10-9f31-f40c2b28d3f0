package com.semptian.ops.server.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;

/**
 * <AUTHOR>
 * Date: 2024/9/18 15:10
 * Description: the config of International
 */
@Configuration
public class InternationalConfig {
    @Value(value = "${spring.messages.basename}")
    private String basename;

    @Bean(name = "messageSource")
    public ResourceBundleMessageSource getMessageResource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        messageSource.setDefaultEncoding("UTF-8");
        String[] basenames = basename.split(",");
        for (String name : basenames) {
            messageSource.addBasenames(name);
        }
        return messageSource;
    }
}
