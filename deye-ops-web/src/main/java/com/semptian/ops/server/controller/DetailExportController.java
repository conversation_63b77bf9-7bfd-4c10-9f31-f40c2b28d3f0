package com.semptian.ops.server.controller;

import com.semptian.base.service.ReturnModel;
import com.semptian.service.DetailExportService;
import com.semptian.service.DetailImportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * 明细导出导入控制器
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Api(tags = "综合搜索HBase明细导出导入API")
@Slf4j
@Validated
@RestController
@RequestMapping("/detail")
public class DetailExportController {

    @Resource
    private DetailExportService detailExportService;

    @Resource
    private DetailImportService detailImportService;

    @ApiOperation(value = "导出HBase明细数据", notes = "根据指标ID和时间范围导出HBase明细数据到CSV文件，metricId可不传，不传时导出所有配置的指标")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "metricId", value = "指标ID，可不传，不传时导出所有配置的指标", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "开始日期，格式：yyyy-MM-dd", required = true, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束日期，格式：yyyy-MM-dd", required = true, dataType = "date", paramType = "query")
    })
    @GetMapping("/export/hbase.json")
    public Object exportHBaseDetail(
            @RequestParam(required = false) Integer metricId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        try {
            log.info("[明细导出] 接收导出请求 | 指标ID: {} | 开始日期: {} | 结束日期: {}", metricId, startDate, endDate);

            // 参数校验
            if (metricId != null && metricId <= 0) {
                return ReturnModel.getInstance().error().setMsg("指标ID必须大于0");
            }

            if (startDate == null) {
                return ReturnModel.getInstance().error().setMsg("开始日期不能为空");
            }

            if (endDate == null) {
                return ReturnModel.getInstance().error().setMsg("结束日期不能为空");
            }

            if (startDate.isAfter(endDate)) {
                return ReturnModel.getInstance().error().setMsg("开始日期不能晚于结束日期");
            }

            // 限制时间范围不超过7天
            if (startDate.plusDays(7).isBefore(endDate)) {
                return ReturnModel.getInstance().error().setMsg("时间范围不能超过7天");
            }

            // 执行导出
            String result = detailExportService.exportHBaseDetail(metricId, startDate, endDate);

            log.info("[明细导出] 导出完成 | 指标ID: {} | 结果: {}", metricId, result);
            return ReturnModel.getInstance().ok(result);

        } catch (Exception e) {
            log.error("[明细导出] 导出失败 | 指标ID: {} | 错误: {}", metricId, e.getMessage(), e);
            return ReturnModel.getInstance().error().setMsg("导出失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "导入CSV文件到Doris", notes = "将明细导出的CSV文件导入到Doris表中")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "metricId", value = "指标ID，可不传，不传时导入所有找到的CSV文件", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "targetDate", value = "目标日期，格式：yyyy-MM-dd，指定要导入哪个日期的CSV文件", required = true, dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "tableName", value = "目标Doris表名，不传时使用默认表名", required = false, dataType = "string", paramType = "query")
    })
    @PostMapping("/import/csv.json")
    public Object importCsvToDoris(
            @RequestParam(required = false) Integer metricId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate targetDate,
            @RequestParam(required = false) String tableName) {

        try {
            log.info("[明细导入] 接收导入请求 | 指标ID: {} | 目标日期: {} | 表名: {}", metricId, targetDate, tableName);

            // 参数校验
            if (metricId != null && metricId <= 0) {
                return ReturnModel.getInstance().error().setMsg("指标ID必须大于0");
            }

            if (targetDate == null) {
                return ReturnModel.getInstance().error().setMsg("目标日期不能为空");
            }

            // 执行导入
            String result = detailImportService.importCsvToDoris(metricId, targetDate, tableName);

            log.info("[明细导入] 导入完成 | 指标ID: {} | 结果: {}", metricId, result);
            return ReturnModel.getInstance().ok(result);

        } catch (Exception e) {
            log.error("[明细导入] 导入失败 | 指标ID: {} | 错误: {}", metricId, e.getMessage(), e);
            return ReturnModel.getInstance().error().setMsg("导入失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "上传CSV文件并导入", notes = "上传CSV文件并直接导入到Doris表中")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "CSV文件", required = true, dataType = "file", paramType = "form"),
            @ApiImplicitParam(name = "metricId", value = "指标ID", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "tableName", value = "目标Doris表名，不传时使用默认表名", required = false, dataType = "string", paramType = "query")
    })
    @PostMapping("/import/upload.json")
    public Object uploadAndImportCsv(
            @RequestParam("file") MultipartFile file,
            @RequestParam Integer metricId,
            @RequestParam(required = false) String tableName) {

        try {
            log.info("[明细导入] 接收文件上传导入请求 | 指标ID: {} | 文件名: {} | 表名: {}",
                    metricId, file.getOriginalFilename(), tableName);

            // 参数校验
            if (metricId == null || metricId <= 0) {
                return ReturnModel.getInstance().error().setMsg("指标ID必须大于0");
            }

            if (file.isEmpty()) {
                return ReturnModel.getInstance().error().setMsg("上传文件不能为空");
            }

            if (!file.getOriginalFilename().toLowerCase().endsWith(".csv")) {
                return ReturnModel.getInstance().error().setMsg("只支持CSV文件格式");
            }

            // 执行上传导入
            String result = detailImportService.uploadAndImportCsv(file, metricId, tableName);

            log.info("[明细导入] 上传导入完成 | 指标ID: {} | 结果: {}", metricId, result);
            return ReturnModel.getInstance().ok(result);

        } catch (Exception e) {
            log.error("[明细导入] 上传导入失败 | 指标ID: {} | 错误: {}", metricId, e.getMessage(), e);
            return ReturnModel.getInstance().error().setMsg("上传导入失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "查询导入任务状态", notes = "查询指定导入任务的执行状态和进度")
    @ApiImplicitParam(name = "taskId", value = "任务ID", required = true, dataType = "string", paramType = "query")
    @GetMapping("/import/status.json")
    public Object getImportTaskStatus(@RequestParam String taskId) {
        try {
            Object status = detailImportService.getImportTaskStatus(taskId);
            return ReturnModel.getInstance().ok(status);
        } catch (Exception e) {
            log.error("[明细导入] 查询任务状态失败 | 任务ID: {} | 错误: {}", taskId, e.getMessage(), e);
            return ReturnModel.getInstance().error().setMsg("查询任务状态失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "导入CSV文件到ops_api_access_result表", notes = "上传CSV文件并导入到ops_api_access_result表中")
    @ApiImplicitParam(name = "file", value = "CSV文件", required = true, dataType = "file", paramType = "form")
    @PostMapping("/import/ops-api-access-result.json")
    public Object importOpsApiAccessResultCsv(@RequestParam("file") MultipartFile file) {

        try {
            log.info("[API访问结果导入] 接收文件上传导入请求 | 文件名: {} | 文件大小: {}",
                    file.getOriginalFilename(), file.getSize());

            // 参数校验
            if (file.isEmpty()) {
                return ReturnModel.getInstance().error().setMsg("上传文件不能为空");
            }

            if (!file.getOriginalFilename().toLowerCase().endsWith(".csv")) {
                return ReturnModel.getInstance().error().setMsg("只支持CSV文件格式");
            }

            // 执行导入
            String result = detailImportService.importOpsApiAccessResultCsv(file);

            log.info("[API访问结果导入] 导入完成 | 结果: {}", result);
            return ReturnModel.getInstance().ok(result);

        } catch (Exception e) {
            log.error("[API访问结果导入] 导入失败 | 错误: {}", e.getMessage(), e);
            return ReturnModel.getInstance().error().setMsg("导入失败: " + e.getMessage());
        }
    }
}
