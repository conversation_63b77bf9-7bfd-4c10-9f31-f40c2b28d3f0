package com.semptian.ops.server.controller;

import com.semptian.base.service.ReturnModel;
import com.semptian.constant.AllConstant;
import com.semptian.service.ApplicationService;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2020/2/28 12:11
 */

@Api(value = "applicationManage")
@RestController
@Slf4j
@CrossOrigin
@RequestMapping("/application")
public class ApplicationController {
    @Resource
    private ApplicationService applicationServer;
    @ApiOperation(value = "根据应用id查询下级菜单和操作信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "应用id，多个id用户逗号分隔", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "userId", value = "用户id", dataType = "Long", paramType = "header"),
            @ApiImplicitParam(name = "keyword", value = "查询关键字", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "isAuth", value = "是否权限控制", dataType = "String", paramType = "query")
    })
    @ApiResponses({
            @ApiResponse(code = 1, message = "响应成功"),
            @ApiResponse(code = 0, message = "响应失败")
    })
    @RequestMapping(value = "/get_menu_operate_by_appid.json", method = RequestMethod.GET)
    public Object getMenuAndOperateByAppid(
            @RequestParam(value = "appId") String appId,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "i18nKeyword", required = false) String[] i18nKeyword,
            @RequestParam(value = "isAuth", required = false, defaultValue = "true") boolean isAuth,
            @RequestParam(value = "appName", required = false) String appName,
            @RequestParam(value = "allPermission", defaultValue = "false", required = false) boolean allPermission,
            HttpServletRequest request
    ) {
        String curUser = request.getHeader(AllConstant.USER_ID_KEY);
        Object o = applicationServer.getMenuAndOperateByAppid(keyword, i18nKeyword, appId, curUser, isAuth,allPermission);
        return new ReturnModel<>().ok(o);
    }

}
