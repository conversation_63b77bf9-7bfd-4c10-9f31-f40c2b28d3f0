package com.semptian.ops.server.controller;

import com.semptian.base.service.ReturnModel;
import com.semptian.service.DataEaseLinkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(value = "DataEase报表链接API")
@Slf4j
@RestController
@RequestMapping("/dataease")
public class DataEaseLinkController {

    @Resource
    private DataEaseLinkService dataEaseLinkService;

    @ApiOperation(value = "获取DataEase报表链接", notes = "根据模块类型过滤报表链接")
    @GetMapping("/api/links.json")
    public Object getDataEaseLinks(@ApiParam(value = "菜单模块 overview 、data-collection-analysis、data-production-analysis、business-data-analysis、real-time-statistics、alarm-query", required = false) @RequestParam(name = "module_type", required = false) String moduleType) {
        return ReturnModel.getInstance().ok(dataEaseLinkService.getLinks(moduleType));
    }
}