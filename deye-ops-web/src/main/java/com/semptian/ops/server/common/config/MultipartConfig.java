package com.semptian.ops.server.common.config;

import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;

import javax.servlet.MultipartConfigElement;

/**
 * 文件上传配置类
 * 用于配置文件上传的大小限制和其他参数
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Configuration
public class MultipartConfig {

    /**
     * 配置文件上传参数
     * 
     * @return MultipartConfigElement 文件上传配置
     */
    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        
        // 单个文件最大大小（1GB）
        factory.setMaxFileSize(DataSize.ofGigabytes(1));
        
        // 整个请求最大大小（1GB）
        factory.setMaxRequestSize(DataSize.ofGigabytes(1));
        
        // 文件写入磁盘的阈值（10MB）
        factory.setFileSizeThreshold(DataSize.ofMegabytes(10));
        
        // 临时文件存储位置
        factory.setLocation("/tmp");
        
        return factory.createMultipartConfig();
    }
}
