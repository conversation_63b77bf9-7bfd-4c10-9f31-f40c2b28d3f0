package com.semptian.ops.server.controller;

import com.semptian.base.service.ReturnModel;
import com.semptian.config.ScheduledTaskConfig;
import com.semptian.service.ScheduledTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 定时任务控制器
 *
 * <AUTHOR>
 * @since 2025/1/21
 */
@Api(tags = "定时任务管理API")
@Slf4j
@Validated
@RestController
@RequestMapping("/scheduled")
public class ScheduledTaskController {

    @Resource
    private ScheduledTaskService scheduledTaskService;

    @ApiOperation(value = "手动触发每日定时任务", notes = "手动执行每日12点的定时任务：获取昨日所有指标明细差异 + 导出昨日所有指标HBase明细数据")
    @PostMapping("/trigger/daily.json")
    public Object triggerDailyTasks() {
        try {
            log.info("[定时任务] 接收手动触发每日定时任务请求");
            
            scheduledTaskService.manualTriggerDailyTasks();
            
            log.info("[定时任务] 手动触发每日定时任务完成");
            return ReturnModel.getInstance().ok("每日定时任务执行完成");
            
        } catch (Exception e) {
            log.error("[定时任务] 手动触发每日定时任务失败", e);
            return ReturnModel.getInstance().error().setMsg("定时任务执行失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "手动触发指定日期的定时任务", notes = "手动执行指定日期的定时任务：获取指定日期所有指标明细差异 + 导出指定日期所有指标HBase明细数据")
    @ApiImplicitParam(name = "targetDate", value = "目标日期，格式：yyyy-MM-dd", required = true, dataType = "date", paramType = "query")
    @PostMapping("/trigger/date.json")
    public Object triggerTasksForDate(@RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate targetDate) {
        try {
            log.info("[定时任务] 接收手动触发指定日期[{}]定时任务请求", targetDate);
            
            // 参数校验
            if (targetDate == null) {
                return ReturnModel.getInstance().error().setMsg("目标日期不能为空");
            }
            
            // 限制日期范围（不能超过当前日期）
            if (targetDate.isAfter(LocalDate.now())) {
                return ReturnModel.getInstance().error().setMsg("目标日期不能超过当前日期");
            }
            
            // 限制日期范围（不能早于30天前）
            if (targetDate.isBefore(LocalDate.now().minusDays(30))) {
                return ReturnModel.getInstance().error().setMsg("目标日期不能早于30天前");
            }
            
            scheduledTaskService.manualTriggerTasksForDate(targetDate);
            
            log.info("[定时任务] 手动触发指定日期[{}]定时任务完成", targetDate);
            return ReturnModel.getInstance().ok("指定日期定时任务执行完成");
            
        } catch (Exception e) {
            log.error("[定时任务] 手动触发指定日期[{}]定时任务失败", targetDate, e);
            return ReturnModel.getInstance().error().setMsg("定时任务执行失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取定时任务配置信息", notes = "获取当前定时任务的配置信息和状态")
    @GetMapping("/info.json")
    public Object getScheduledTaskInfo() {
        try {
            // 获取实际配置信息
            ScheduledTaskConfig config = scheduledTaskService.getTaskConfig();

            // 构建定时任务信息
            Map<String, Object> taskInfo = new HashMap<>();
            taskInfo.put("taskName", "每日指标差异分析和明细导出任务");
            taskInfo.put("cronExpression", config.getCron());
            taskInfo.put("description", config.getDescription());
            taskInfo.put("enabled", config.isEnabled());
            taskInfo.put("nextExecutionTime", "每天12:00:00");

            List<Map<String, Object>> tasks = new ArrayList<>();

            Map<String, Object> task1 = new HashMap<>();
            task1.put("order", 1);
            task1.put("name", "获取昨日所有指标明细差异");
            task1.put("description", "调用系统间一致性结果对比服务，分析昨日所有指标的差异数据");
            tasks.add(task1);

            Map<String, Object> task2 = new HashMap<>();
            task2.put("order", 2);
            task2.put("name", "导出昨日所有指标HBase明细数据");
            task2.put("description", "在第一个任务完成后，导出昨日所有指标的HBase明细数据到CSV文件");
            tasks.add(task2);

            taskInfo.put("tasks", tasks);

            return ReturnModel.getInstance().ok(taskInfo);

        } catch (Exception e) {
            log.error("[定时任务] 获取定时任务信息失败", e);
            return ReturnModel.getInstance().error().setMsg("获取定时任务信息失败: " + e.getMessage());
        }
    }
}
