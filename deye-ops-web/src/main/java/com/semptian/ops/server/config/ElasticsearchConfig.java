package com.semptian.ops.server.config;

import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration;
import org.springframework.context.annotation.Configuration;

/**
 * Elasticsearch配置类
 * 禁用Spring Boot的Elasticsearch自动配置，避免健康检查连接到默认的localhost:9200
 */
@Configuration
@EnableAutoConfiguration(exclude = {ElasticsearchRestClientAutoConfiguration.class})
public class ElasticsearchConfig {
    // 空配置类，仅用于禁用Spring Boot的Elasticsearch自动配置
}
