package com.semptian.ops.server.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.semptian.base.service.ReturnModel;
import com.semptian.entity.OpsApiAccessResultDifference;
import com.semptian.model.vo.AdsOpsOpsApiAccessResult;
import com.semptian.service.InterSystemDataCompareService;
import com.semptian.service.InterSystemDataProducerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Vector;

@Api(tags = "系统间一致性指标生产API")
@Slf4j
@Validated
@RestController
@RequestMapping("/metrics/producer")
public class InterSystemDataProducerController {
    @Value("${inter_system.metric.size:17}")
    private int metricSize;

    @Resource
    private InterSystemDataProducerService interSystemDataProducerService;

    @Resource
    private InterSystemDataCompareService interSystemDataCompareService;


    @ApiOperation(value = "指标采集", notes = "触发系统间一致性指标采集")
    @ApiImplicitParams({@ApiImplicitParam(name = "collectTimeRange", value = "采集时间范围,默认为昨日，格式为2025-03-01,2025-03-07", dataType = "string", paramType = "query"), @ApiImplicitParam(name = "metricId", value = "指标ID", dataType = "int", paramType = "query")})
    @GetMapping("/collect.json")
    public Object collectMetrics(@RequestParam(required = false) String collectTimeRange, @RequestParam(required = false) Integer metricId, @RequestParam(required = false, defaultValue = "false") boolean isAll) {
        List<AdsOpsOpsApiAccessResult> results = new Vector<>();
        //默认为昨日
        if (null == collectTimeRange || collectTimeRange.isEmpty()) {
            java.time.LocalDate yesterday = java.time.LocalDate.now().minusDays(1);
            String yesterdayStr = yesterday.toString();
            collectTimeRange = yesterdayStr + "," + yesterdayStr;
        }
        if (isAll) {
            for (int i = 1; i <= metricSize; i++) {
                results.addAll(interSystemDataProducerService.collectMetrics(i, collectTimeRange));
            }
        } else {
            results.addAll(interSystemDataProducerService.collectMetrics(metricId, collectTimeRange));
        }
        return ReturnModel.getInstance().ok(results);
    }

    @ApiOperation(value = "根据采样配置生产采样账号", notes = "根据采样配置生产采样账号")
    @ApiImplicitParams({@ApiImplicitParam(name = "maxSampleCount", value = "最大采样数量", dataType = "int", paramType = "query")})
    @GetMapping("/produce.json")
    public Object produceSampleAccount(@RequestParam(defaultValue = "200000") Integer maxSampleCount) {
        //采样生产改为异步，避免过多的HTTP阻塞请求
        log.debug("开始执行采样生产任务");
        interSystemDataProducerService.produceSampleAccount(maxSampleCount);
        log.debug("采样生产任务执行完成");
        return ReturnModel.getInstance().ok();
    }

    @ApiOperation(value = "根据采样配置及日期更新采样账号", notes = "根据采样配置及日期更新采样账号")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "maxSampleCount", value = "最大采样数量", dataType = "int", paramType = "query"),
        @ApiImplicitParam(name = "sampleDate", value = "采样日期，格式为yyyy-MM-dd，默认为当天", dataType = "string", paramType = "query")
    })
    @GetMapping("/update_by_date.json")
    public Object updateSampleAccountByDate(@RequestParam(defaultValue = "200000") Integer maxSampleCount,
                                          @RequestParam(required = false) String sampleDate) {
        //默认为昨天
        if (null == sampleDate || sampleDate.isEmpty()) {
            sampleDate = LocalDate.now().minusDays(1).toString();
        }
        log.debug("开始执行根据日期[{}]更新采样账号任务，最大采样数量：{}", sampleDate, maxSampleCount);
        boolean result = interSystemDataProducerService.updateSampleAccountByDate(maxSampleCount, sampleDate);
        log.debug("根据日期更新采样账号任务执行完成，结果：{}", result);
        return ReturnModel.getInstance().ok(result);
    }

    @ApiOperation(value = "系统间一致性结果对比计算", notes = "触发系统间一致性结果对比计算,输出差异结果到Doris表ops_api_access_result_difference")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "timeRange", value = "结果对比时间范围,默认为昨日，格式为2025-05-20,2025-05-20", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "metricId", value = "指标ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "account", value = "统计账号", dataType = "string", paramType = "query")})
    @GetMapping("/compare_result.json")
    public Object compareResult(@RequestParam(required = false) String timeRange,
                                @RequestParam(required = false) Integer metricId,
                                @RequestParam(required = false) String account) {

        List<OpsApiAccessResultDifference> results = new ArrayList<>();

        //时间范围默认为昨日
        if (StrUtil.isEmpty(timeRange)) {
            java.time.LocalDate yesterday = java.time.LocalDate.now().minusDays(1);
            String yesterdayStr = yesterday.toString();
            timeRange = yesterdayStr + "," + yesterdayStr;
        }

        //时间范围校验
        if (ArrayUtil.isEmpty(timeRange.split(",")) || timeRange.split(",").length != 2) {
            return ReturnModel.getInstance().error().setMsg("timeRange format error");
        }

        //时间格式校验
        LocalDate startDate;
        LocalDate endDate;
        try {
            startDate = LocalDate.parse(timeRange.split(",")[0]);
            endDate = LocalDate.parse(timeRange.split(",")[1]);
        } catch (Exception e) {
            return ReturnModel.getInstance().error().setMsg("time format error");
        }

        if (null == metricId) {
            for (int i = 1; i <= metricSize; i++) {
                results.addAll(interSystemDataCompareService.compareResult(i, startDate, endDate, account));
            }
        } else {
            results.addAll(interSystemDataCompareService.compareResult(metricId, startDate, endDate, account));
        }
        return ReturnModel.getInstance().ok();
    }
}