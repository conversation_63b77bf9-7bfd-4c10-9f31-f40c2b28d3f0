# 定时任务功能说明

## 概述

本系统新增了定时任务功能，每天上午12点自动执行两个关键任务：
1. 获取昨日所有指标明细差异
2. 导出昨日所有指标HBase明细数据

**技术要求**: 使用Java 8语法实现，兼容JDK 1.8及以上版本。

## 功能特性

### 自动执行
- **执行时间**: 每天上午12:00:00
- **Cron表达式**: `0 0 12 * * ?`
- **处理日期**: 自动处理昨天的数据

### 任务内容

#### 任务1：获取昨日所有指标明细差异
- 自动获取系统中所有启用状态的指标ID
- 对每个指标调用系统间一致性结果对比服务
- 分析昨日数据的差异情况
- 将差异结果写入Doris数据库

#### 任务2：导出昨日所有指标HBase明细数据
- 在任务1完成后自动执行
- 导出所有配置指标的HBase明细数据
- 生成CSV文件并保存到指定目录
- 支持分布式锁防止重复执行

## 配置说明

### 配置文件位置
`deye-ops-web/src/main/resources/bootstrap.yml`

### 配置项说明
```yaml
# 定时任务配置
scheduled:
  tasks:
    # 每日指标差异分析和明细导出任务
    daily:
      # 每天上午12点执行
      cron: "0 0 12 * * ?"
      enabled: true
      description: "每天上午12点自动执行：1.获取昨日所有指标明细差异 2.导出昨日所有指标HBase明细数据"
```

### 配置项详解
- `cron`: Cron表达式，控制任务执行时间
- `enabled`: 是否启用定时任务，设置为false可禁用
- `description`: 任务描述信息

## API接口

### 1. 手动触发每日定时任务
```
POST /ops/scheduled/trigger/daily.json
```
**功能**: 手动执行每日12点的定时任务
**返回**: 执行结果信息

### 2. 手动触发指定日期的定时任务
```
POST /ops/scheduled/trigger/date.json?targetDate=2025-01-20
```
**参数**:
- `targetDate`: 目标日期，格式：yyyy-MM-dd

**限制**:
- 不能超过当前日期
- 不能早于30天前

**功能**: 手动执行指定日期的定时任务

### 3. 获取定时任务配置信息
```
GET /ops/scheduled/info.json
```
**功能**: 获取当前定时任务的配置信息和状态
**返回**: 包含任务配置、状态和子任务详情

## 日志说明

### 日志级别
- INFO: 任务开始、完成、进度信息
- ERROR: 任务执行失败、异常信息
- DEBUG: 详细的执行过程信息

### 日志示例
```
2025-01-21 12:00:00 INFO  [ScheduledTaskService] === 开始执行每日12点定时任务 ===
2025-01-21 12:00:00 INFO  [ScheduledTaskService] 处理日期: 2025-01-20
2025-01-21 12:00:01 INFO  [ScheduledTaskService] --- 开始执行任务1：获取昨日所有指标明细差异 ---
2025-01-21 12:00:01 INFO  [ScheduledTaskService] 找到指标数量: 16
2025-01-21 12:00:02 INFO  [ScheduledTaskService] 开始处理指标[1]的差异分析
2025-01-21 12:00:03 INFO  [ScheduledTaskService] 指标[1]差异分析完成，发现差异数量: 25
...
2025-01-21 12:05:30 INFO  [ScheduledTaskService] --- 任务1执行完成 | 成功: 16 | 失败: 0 ---
2025-01-21 12:05:30 INFO  [ScheduledTaskService] --- 开始执行任务2：导出昨日所有指标HBase明细数据 ---
2025-01-21 12:15:45 INFO  [ScheduledTaskService] --- 任务2执行完成 | 结果: 导出完成，共处理16个指标 ---
2025-01-21 12:15:45 INFO  [ScheduledTaskService] === 每日12点定时任务执行完成 ===
```

## 错误处理

### 异常情况处理
1. **指标配置为空**: 跳过差异分析任务，记录警告日志
2. **单个指标处理失败**: 记录错误日志，继续处理其他指标
3. **任务2执行失败**: 记录错误日志，不影响下次定时执行

### 分布式锁
- 使用Redis分布式锁防止重复执行
- 锁超时时间根据任务执行时间自动调整
- 支持锁释放和异常处理

## 监控建议

### 关键监控指标
1. 任务执行成功率
2. 任务执行耗时
3. 处理的指标数量
4. 发现的差异数量
5. 导出的文件大小

### 告警设置
1. 任务执行失败告警
2. 任务执行超时告警（建议设置为30分钟）
3. 差异数量异常告警
4. 磁盘空间不足告警

## Java 8语法特性

本定时任务功能严格使用Java 8语法实现，主要特性包括：

### 1. Lambda表达式和Stream API
```java
// 使用Stream API处理指标ID列表
return configs.stream()
        .map(config -> config.getId().intValue())
        .collect(Collectors.toList());
```

### 2. 类型推断
```java
// 使用明确的类型声明，避免var关键字
List<OpsApiAccessResultDifference> differences = interSystemDataCompareService.compareResult(...);
Map<String, Object> taskInfo = new HashMap<>();
List<Map<String, Object>> tasks = new ArrayList<>();
```

### 3. 时间API
```java
// 使用java.time包的LocalDate
LocalDate yesterday = LocalDate.now().minusDays(1);
LocalDate targetDate = LocalDate.of(2025, 1, 20);
```

### 4. 注解支持
```java
// 使用@Scheduled注解实现定时任务
@Scheduled(cron = "0 0 12 * * ?")
@ConditionalOnProperty(name = "scheduled.tasks.daily.enabled", havingValue = "true", matchIfMissing = true)
public void executeDaily12PMTasks() { ... }
```

## 注意事项

1. **Java版本**: 确保运行环境为JDK 1.8或更高版本
2. **时间设置**: 建议在业务低峰期执行，避免影响系统性能
3. **磁盘空间**: 确保导出目录有足够的磁盘空间
4. **数据库连接**: 确保Doris和MySQL连接正常
5. **HBase连接**: 确保HBase集群连接正常
6. **配置更新**: 修改配置后需要重启应用生效

## 故障排查

### 常见问题
1. **定时任务不执行**: 检查`enabled`配置和应用启动日志
2. **任务执行失败**: 查看错误日志，检查数据库连接
3. **导出文件为空**: 检查HBase连接和差异数据是否存在
4. **内存不足**: 调整JVM参数或优化批处理大小

### 日志查看
```bash
# 查看定时任务相关日志
grep "ScheduledTaskService" /path/to/logs/application.log

# 查看特定日期的任务执行情况
grep "2025-01-20" /path/to/logs/application.log | grep "ScheduledTaskService"
```
