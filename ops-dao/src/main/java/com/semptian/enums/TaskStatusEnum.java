package com.semptian.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

@Getter
public enum TaskStatusEnum {
    PENDING(0, "待执行"),
    EXECUTING(1, "执行中"),
    SUCCESS(2, "执行成功"),
    FAILED(3, "执行失败");

    @EnumValue
    private final int code;
    private final String description;

    TaskStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }
}