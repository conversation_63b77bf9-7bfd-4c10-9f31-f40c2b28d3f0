package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.DataEaseLink;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface DataEaseLinkMapper extends BaseMapper<DataEaseLink> {

    //如果module_type为空，查询所有
    @Select({"SELECT ", "id, module_type, dashboard_name, embed_url ", "FROM tb_ops_dataease_link ", "WHERE (module_type = #{moduleType} OR #{moduleType} IS NULL)"})
    List<DataEaseLink> selectLinks(@Param("moduleType") String moduleType);
}