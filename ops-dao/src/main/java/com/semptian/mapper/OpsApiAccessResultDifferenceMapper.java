package com.semptian.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.OpsApiAccessResultDifference;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 系统间一致性统计结果差异Mapper
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
@Mapper
@DS("doris")
public interface OpsApiAccessResultDifferenceMapper extends BaseMapper<OpsApiAccessResultDifference> {

    @Delete({"<script>" + "DELETE FROM ops_api_access_result_difference WHERE " + "metric_id = #{metricId} " + "AND stat_date BETWEEN #{startDate} AND #{endDate} " + "<if test=\"account !=null and account !=''\"> ", "AND account = #{account} " + "</if> ", "</script>"})
    void deleteByMetricIdAndTimeRange(@Param("metricId") Integer metricId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("account") String account);

    /**
     * 使用原生SQL查询差异账号，避免MyBatis-Plus在Doris中的兼容性问题
     */
    @Select("SELECT metric_id, metric_name, stat_date, account, is_kv, kv_content, different_kv_content " + "FROM ops_api_access_result_difference " + "WHERE metric_id = #{metricId} " + "AND stat_date BETWEEN #{startDate} AND #{endDate} ")
    List<OpsApiAccessResultDifference> selectDifferenceAccountsBySql(@Param("metricId") Integer metricId, @Param("startDate") Date startDate, @Param("endDate") Date endDate);
}
