package com.semptian.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.semptian.entity.SpamCallNumberInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 骚扰号码信息Mapper接口
 */
@Mapper
public interface SpamCallNumberInfoMapper extends BaseMapper<SpamCallNumberInfo> {

    /**
     * 获取所有未删除的骚扰号码
     *
     * @param databaseName 数据库名称
     * @return 骚扰号码列表
     */
    @Select("SELECT * FROM `${databaseName}`.tb_spam_call_number_info WHERE is_del = 0")
    List<SpamCallNumberInfo> getAllActiveSpamNumbers(@Param("databaseName") String databaseName);

    /**
     * 分批获取未删除的骚扰号码
     *
     * @param databaseName 数据库名称
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 骚扰号码列表
     */
    @Select("SELECT * FROM `${databaseName}`.tb_spam_call_number_info WHERE is_del = 0 LIMIT #{offset}, #{limit}")
    List<SpamCallNumberInfo> getBatchActiveSpamNumbers(@Param("databaseName") String databaseName,
                                                      @Param("offset") int offset,
                                                      @Param("limit") int limit);

    /**
     * 检查指定号码是否存在于骚扰号码库中
     *
     * @param databaseName 数据库名称
     * @param phoneNumber 电话号码
     * @return 存在的记录数量
     */
    @Select("SELECT COUNT(1) FROM `${databaseName}`.tb_spam_call_number_info " +
            "WHERE is_del = 0 AND (phone_number = #{phoneNumber} OR phone_number LIKE CONCAT('%,', #{phoneNumber}, ',%') " +
            "OR phone_number LIKE CONCAT(#{phoneNumber}, ',%') OR phone_number LIKE CONCAT('%,', #{phoneNumber}))")
    int existsSpamNumber(@Param("databaseName") String databaseName, @Param("phoneNumber") String phoneNumber);
}
