package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.semptian.enums.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tb_ops_task")
public class TaskEntity {
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long jobTaskId;
    
    private String taskName;
    
    private String chartName;
    
    private String taskDesc;
    
    @EnumValue
    private ModuleNameEnum moduleName;
    
    @EnumValue
    private TimeModeEnum timeMode;
    
    @EnumValue
    private TaskStatusEnum status;
    
    private String executeRange;
    
    private Date lastExecEndtime;
    
    private String submitUser;
    
    private Date submitTime;
    
    private Date completeTime;
    
    private String errorLog;
    
    private Long createTime;
    
    private Long modifyTime;
}