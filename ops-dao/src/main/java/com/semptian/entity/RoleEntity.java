package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 角色实体类
 *
 * <AUTHOR>
 * @date 2020/2/28 11:44
 */
@Data
@TableName("tb_portal_role")
public class RoleEntity {
    /**
     * 角色id
     */
    @TableId
    private String id;
    /**
     * 角色名称
     */
    @TableField("role_name")
    private String roleName;
    /**
     * 角色描述
     */
    @TableField("role_desc")
    private String roleDesc;
    /**
     * 角色下用户数
     */
    @TableField("role_user_num")
    private Integer roleUserNum;
    /**
     * 角色所属职位id
     */
    @TableField("position_id")
    private Integer positionId;

    /**
     * 是否是预置角色
     */
    @TableField("default_role")
    private Integer defaultRole;

    /**
     * 绑定的组织
     */
    @TableField("bind_org")
    private Integer bindOrg;

    /**
     * 绑定组织名称
     */
    @TableField(exist = false)
    private String orgName;

    /**
     * 角色创建时间
     */
    @TableField("create_time")
    private Long createTime;
    /**
     * 角色创建用户
     */
    @TableField("create_user")
    private String createUser;
    /**
     * 修改用户
     */
    @TableField("modify_user")
    private String modifyUser;
    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Long modifyTime;

    /**
     * 绑定的用户组
     */
    @TableField("bind_group")
    private Integer groupId;

    /**
     * 绑定用户组名称
     */
    @TableField(exist = false)
    private String groupName;

    /**
     * 绑定用户组名称
     */
    @TableField(exist = false)
    private String positionType;

    /**
     * 绑定职位名称，用于页面展示
     */
    @TableField(exist = false)
    private String positionName;
}
