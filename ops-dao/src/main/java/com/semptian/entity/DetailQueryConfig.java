package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 明细查询配置表
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_ops_detail_query_config")
public class DetailQueryConfig {

    @TableId(type = IdType.AUTO)
    private Integer id;

    @TableField("metric_id")
    private Integer metricId;

    @TableField("system_name")
    private String systemName;

    @TableField("req_type")
    private Integer reqType;

    @TableField("req_path")
    private String reqPath;

    @TableField("params_template")
    private String paramsTemplate;

    @TableField("response_mapping")
    private String responseMapping;

    @TableField("create_time")
    private Long createTime;

    @TableField("modify_time")
    private Long modifyTime;
}
