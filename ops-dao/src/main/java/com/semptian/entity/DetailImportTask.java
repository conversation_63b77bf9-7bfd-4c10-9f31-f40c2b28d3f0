package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 明细导入任务表
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_ops_detail_import_task")
public class DetailImportTask {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("task_id")
    private String taskId;

    @TableField("metric_id")
    private Integer metricId;

    @TableField("target_date")
    private LocalDate targetDate;

    @TableField("table_name")
    private String tableName;

    @TableField("file_path")
    private String filePath;

    @TableField("file_name")
    private String fileName;

    @TableField("file_size")
    private Long fileSize;

    @TableField("total_rows")
    private Long totalRows;

    @TableField("processed_rows")
    private Long processedRows;

    @TableField("success_rows")
    private Long successRows;

    @TableField("failed_rows")
    private Long failedRows;

    @TableField("status")
    private Integer status; // 0=待处理, 1=处理中, 2=成功, 3=失败, 4=部分成功

    @TableField("error_message")
    private String errorMessage;

    @TableField("start_time")
    private Long startTime;

    @TableField("end_time")
    private Long endTime;

    @TableField("create_time")
    private Long createTime;

    @TableField("modify_time")
    private Long modifyTime;

    /**
     * 任务状态枚举
     */
    public enum Status {
        PENDING(0, "待处理"),
        PROCESSING(1, "处理中"),
        SUCCESS(2, "成功"),
        FAILED(3, "失败"),
        PARTIAL_SUCCESS(4, "部分成功");

        private final int code;
        private final String description;

        Status(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(int code) {
            for (Status status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown status code: " + code);
        }
    }
}
