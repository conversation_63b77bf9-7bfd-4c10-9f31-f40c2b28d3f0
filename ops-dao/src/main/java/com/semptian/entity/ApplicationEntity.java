package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/2/28 11:44
 */
@Data
@TableName("tb_portal_app")
public class ApplicationEntity {
    /**
     * 应
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 应用
     */
    @TableField("app_name")
    private String appName;
    /**
     * 应用url
     */
    @TableField("app_url")
    private String appUrl;
    /**
     * 应用状态
     * 1.可用
     * 2.停用
     */
    @TableField("app_state")
    private Integer appState;
    /**
     * 应用状态名称
     */
    @TableField(exist = false)
    private String appStateName;

    /**
     * 应用类型id
     */
    @TableField("app_type_id")
    private String appTypeId;

    /**
     * 应用类型名称
     */
    @TableField(exist = false)
    private String appTypeName;

    /**
     * 应用版本
     */
    @TableField("app_version")
    private String appVersion;
    /**
     * 应用备注
     */
    @TableField("app_desc")
    private String appDesc;
    /**
     * 应用图片路径
     */
    @TableField("app_img")
    private String appImg;
    /**
     * 应用菜单数
     */
    @TableField("app_menu_num")
    private Integer appMenuNum;
    /**
     * 应用操作数
     */
    @TableField("app_operate_num")
    private Integer appOperateNum;
    /**
     * 删除状态 0正常;1删除
     */
    @TableField("del_status")
    private Integer delStatus;
    /**
     * 最近使用时间
     */
    @TableField("use_time")
    private Long useTime;
    /**
     * 应用创建时间
     */
    @TableField("create_time")
    private Long createTime;
    /**
     * 应用创建用户
     */
    @TableField("create_user")
    private String createUser;
    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Long modifyTime;
    /**
     * 修改用户
     */
    @TableField("modify_user")
    private String modifyUser;

    @TableField("`order`")
    private Integer order;

    @TableField("app_shorthand")
    private String appShorthand;
    /**
     * 是否为预置系统
     * 0=是
     * 1=否
     */
    @TableField("is_default")
    private Integer isDefault;

    @TableField(exist = false)
    private Integer isHide = 0;
}
