package com.semptian.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/9/29 20:49
 * @description  用户心跳表
 */
@Data
@TableName("tb_portal_user_heartbeat")
public class UserHeartbeatEntity {
    @TableId
    @TableField("user_id")
    private Long userId;

    @TableField("account")
    private String account;

    @TableField("token")
    private String token;

    @TableField("update_time")
    private Long updateTime;
}
