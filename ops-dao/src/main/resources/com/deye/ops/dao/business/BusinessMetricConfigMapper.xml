<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.semptian.mapper.BusinessMetricConfigMapper">
    <sql id="searchCondition">
        <where>
            <if test="metricModelName != null and metricModelName != ''">
                AND metric_model_name LIKE CONCAT('%', #{metricModelName}, '%')
            </if>
            <if test="metricName != null and metricName != ''">
                AND metric_name LIKE CONCAT('%', #{metricName}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </sql>

    <select id="selectPageList" resultType="com.semptian.entity.BusinessMetricConfig">
        SELECT * FROM tb_ops_business_metric
        <include refid="searchCondition"/>
        ORDER BY create_time DESC
    </select>
</mapper>