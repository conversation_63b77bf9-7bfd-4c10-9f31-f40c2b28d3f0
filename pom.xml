<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.semptian</groupId>
    <artifactId>deye-ops-service</artifactId>
    <packaging>pom</packaging>
    <version>4.0.0-SNAPSHOT</version>
    <modules>
        <module>ops-core</module>
        <module>ops-dao</module>
        <module>ops-service</module>
        <module>deye-ops-web</module>
    </modules>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.2.RELEASE</version>
        <relativePath/>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <swagger.version>2.6.1</swagger.version>
        <base-core.version>4.0.0-SNAPSHOT</base-core.version>
        <base-dao.version>4.0.0-SNAPSHOT</base-dao.version>
        <base-log.version>4.0.1-SNAPSHOT</base-log.version>
        <base-web.version>4.0.0-SNAPSHOT</base-web.version>
        <base-redis.version>4.0.0-SNAPSHOT</base-redis.version>
        <base-tianhe.version>4.1.7-SNAPSHOT</base-tianhe.version>
        <ops-core>4.0.0-SNAPSHOT</ops-core>
        <ops-dao>4.0.0-SNAPSHOT</ops-dao>
        <ops-service>4.0.0-SNAPSHOT</ops-service>
        <ops-connector-doris>4.0.0-SNAPSHOT</ops-connector-doris>
        <ops-connector-nebula>4.0.0-SNAPSHOT</ops-connector-nebula>
        <mysql-connector-java.version>5.1.45</mysql-connector-java.version>
        <mybatisplus.boot-starter.version>3.4.0</mybatisplus.boot-starter.version>
        <mybatis-plus.version>3.4.0</mybatis-plus.version>
        <spring-cloud.version>2.2.6.RELEASE</spring-cloud.version>
        <spring-cloud-starter-openfeign.version>2.2.6.RELEASE</spring-cloud-starter-openfeign.version>
        <jsch.version>0.1.55</jsch.version>
        <dynamic-datasource.version>3.2.1</dynamic-datasource.version>
        <ganymed-ssh2.version>build210</ganymed-ssh2.version>
        <hutool-all.version>4.3.1</hutool-all.version>
        <admin-server.version>1.5.7</admin-server.version>
        <jedis.version>3.3.0</jedis.version>
        <druid.version>1.1.13</druid.version>
    </properties>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <target>${java.version}</target>
                    <source>${java.version}</source>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <rules>
                        <rule>
                            <element>BUNDLE</element>
                            <limits>
                                <limit>
                                    <counter>LINE</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>0.80</minimum>
                                </limit>
                            </limits>
                        </rule>
                    </rules>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>1.9.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compileTests</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M7</version>
                <configuration>
                    <!-- 配置单测失败几次后停止执行 -->
                    <skipAfterFailureCount>0</skipAfterFailureCount>
                    <!-- 不允许跳过单测 -->
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>Releases</name>
            <url>http://*************:8081/nexus/content/repositories/releases/</url>
            <uniqueVersion>true</uniqueVersion>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Snapshots</name>
            <url>http://*************:8081/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>