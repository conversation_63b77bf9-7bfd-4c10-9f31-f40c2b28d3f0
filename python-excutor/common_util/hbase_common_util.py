import happybase
import json
import typing
import math
from datetime import datetime, timedelta

# 制定每次请求hbase的批次
BATCH = 100


class HbaseConnect():
    def __init__(self, hbase_conf=None):
        if hbase_conf:
            if type(hbase_conf) is str:
                self.hbase_conf = json.loads(hbase_conf)
            else:
                self.hbase_conf = hbase_conf
            self.connection = happybase.Connection(
                # *************
                host=self.hbase_conf["host"],
                # 9090
                port=self.hbase_conf["port"],
                autoconnect=True
            )

    def get_rows_count(self, tables, rows: typing.List) -> int:
        if len(rows) == 0:
            return 0
        batch_size = math.ceil(len(rows) / BATCH)
        count_num = 0
        for table in tables:
            tab = self.connection.table(table)
            for i in range(0, batch_size):
                # 按照对应的批次获取id集合批次查询hbase
                st = i * BATCH
                rows_batch = rows[st:st + BATCH]
                query_rows = tab.rows(rows=rows_batch)
                count_num += len(query_rows)
        return count_num


def generate_hbase_table_suffixes(resource_names: str, captureStartDay, captureEndDay) -> set[str]:
    """
    根据传入的时间参数，计算明细表的时间后缀。并替换
    :param resource_names:
    :param captureStartDay:
    :param captureEndDay:
    :return:
    """
    try:
        start_date = datetime.strptime(captureStartDay, "%Y-%m-%d")
        end_date = datetime.strptime(captureEndDay, "%Y-%m-%d")
    except ValueError:
        raise ValueError("时间格式必须为 'YYYY-MM-DD'")
        # 校验时间跨度 ≤7 天
    delta = end_date - start_date
    if delta.days < 0:
        raise ValueError("结束时间不能早于起始时间")
    if delta.days > 6:  # 最大7天（含起始和结束）
        raise ValueError("时间跨度不能超过7天")
    # 遍历日期范围，收集所有涉及的月份
    suffixes = set()
    current_date = start_date
    while current_date <= end_date:
        # 生成索引后缀（格式：YYYYMM）
        suffix = current_date.strftime("%Y%m")
        suffixes.add(suffix)
        current_date += timedelta(days=1)
    # 按时间顺序排序并返回
    sorted_suffixes = sorted(suffixes)
    detail_table_names = set()
    for suffix in sorted_suffixes:
        for resource_name in resource_names.split(','):
            detail_table_names.add("dw:" + resource_name + "_detail_" + suffix)
    return detail_table_names
