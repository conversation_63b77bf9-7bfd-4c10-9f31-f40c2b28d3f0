data_type_mapping = {
    "100": {"original_data_alias": "HTTP", "data_type_code": "100", "data_type_alias": "HTTP"},
    "101": {"original_data_alias": "<PERSON>ail", "data_type_code": "101", "data_type_alias": "<PERSON><PERSON>"},
    "102": {"original_data_alias": "AUTH", "data_type_code": "102", "data_type_alias": "AUTH"},
    "103": {"original_data_alias": "IM", "data_type_code": "103", "data_type_alias": "IM"},
    "104": {"original_data_alias": "GAME", "data_type_code": "2003", "data_type_alias": "Entertainment"},
    "105": {"original_data_alias": "FTP", "data_type_code": "105", "data_type_alias": "FTP"},
    "106": {"original_data_alias": "WEBCHAT", "data_type_code": "106", "data_type_alias": "WEBCHA<PERSON>"},
    "107": {"original_data_alias": "WEBBBS", "data_type_code": "119", "data_type_alias": "SNS"},
    "108": {"original_data_alias": "Telnet", "data_type_code": "108", "data_type_alias": "Telnet"},
    "2109": {"original_data_alias": "AppCall", "data_type_code": "2109", "data_type_alias": "AppCall"},
    "110": {"original_data_alias": "OVERNET", "data_type_code": "121", "data_type_alias": "VPN"},
    "111": {"original_data_alias": "PROXY", "data_type_code": "111", "data_type_alias": "PROXY"},
    "112": {"original_data_alias": "P2P", "data_type_code": "2004", "data_type_alias": "Tool"},
    "113": {"original_data_alias": "RemoteCTRL", "data_type_code": "113", "data_type_alias": "RemoteCTRL"},
    "115": {"original_data_alias": "BLOG", "data_type_code": "119", "data_type_alias": "SNS"},
    "116": {"original_data_alias": "STREAMMEDIA", "data_type_code": "2002", "data_type_alias": "Multimedia"},
    "117": {"original_data_alias": "MMS", "data_type_code": "117", "data_type_alias": "MMS"},
    "118": {"original_data_alias": "HTTPS", "data_type_code": "118", "data_type_alias": "HTTPS"},
    "119": {"original_data_alias": "SNS", "data_type_code": "119", "data_type_alias": "SNS"},
    "120": {"original_data_alias": "SMS", "data_type_code": "120", "data_type_alias": "SMS"},
    "121": {"original_data_alias": "VPN", "data_type_code": "121", "data_type_alias": "VPN"},
    "122": {"original_data_alias": "Shopping", "data_type_code": "122", "data_type_alias": "Shopping"},
    "123": {"original_data_alias": "TRAVELORDER", "data_type_code": "123", "data_type_alias": "TRAVELORDER"},
    "124": {"original_data_alias": "DELIVERY", "data_type_code": "124", "data_type_alias": "DELIVERY"},
    "125": {"original_data_alias": "Engine", "data_type_code": "125", "data_type_alias": "Engine"},
    "126": {"original_data_alias": "FLIGHT", "data_type_code": "126", "data_type_alias": "FLIGHT"},
    "127": {"original_data_alias": "WEBLOGIN", "data_type_code": "999", "data_type_alias": "Other"},
    "128": {"original_data_alias": "WEBSHARE", "data_type_code": "2004", "data_type_alias": "Tool"},
    "129": {"original_data_alias": "THIRD-PARTY_PAYMENT", "data_type_code": "963", "data_type_alias": "Finance"},
    "131": {"original_data_alias": "NETBANK", "data_type_code": "963", "data_type_alias": "Finance"},
    "132": {"original_data_alias": "PHONEBOOK", "data_type_code": "132", "data_type_alias": "PHONEBOOK"},
    "133": {"original_data_alias": "MBLOG", "data_type_code": "119", "data_type_alias": "SNS"},
    "134": {"original_data_alias": "MBLOG_AT", "data_type_code": "134", "data_type_alias": "MBLOG_AT"},
    "135": {"original_data_alias": "MBLOG_TOPIC", "data_type_code": "135", "data_type_alias": "MBLOG_TOPIC"},
    "136": {"original_data_alias": "MBLOG_URL", "data_type_code": "136", "data_type_alias": "MBLOG_URL"},
    "137": {"original_data_alias": "MBLOG_ACTION", "data_type_code": "137", "data_type_alias": "MBLOG_ACTION"},
    "138": {"original_data_alias": "News", "data_type_code": "138", "data_type_alias": "News"},
    "139": {"original_data_alias": "DVE_VOICE", "data_type_code": "2002", "data_type_alias": "Multimedia"},
    "140": {"original_data_alias": "VIDEO", "data_type_code": "2002", "data_type_alias": "Multimedia"},
    "141": {"original_data_alias": "PICTURE", "data_type_code": "2002", "data_type_alias": "Multimedia"},
    "142": {"original_data_alias": "Terminal", "data_type_code": "142", "data_type_alias": "Terminal"},
    "143": {"original_data_alias": "TRADE", "data_type_code": "143", "data_type_alias": "TRADE"},
    "144": {"original_data_alias": "MAP", "data_type_code": "2001", "data_type_alias": "Travel"},
    "146": {"original_data_alias": "Location", "data_type_code": "146", "data_type_alias": "Location"},
    "148": {"original_data_alias": "DNS", "data_type_code": "148", "data_type_alias": "DNS"},
    "151": {"original_data_alias": "NAVIGATION", "data_type_code": "2001", "data_type_alias": "Travel"},
    "152": {"original_data_alias": "NET_ORDER", "data_type_code": "152", "data_type_alias": "NET_ORDER"},
    "153": {"original_data_alias": "CAR_RENT", "data_type_code": "153", "data_type_alias": "CAR_RENT"},
    "154": {"original_data_alias": "TRIP", "data_type_code": "154", "data_type_alias": "TRIP"},
    "155": {"original_data_alias": "NET_LIVE", "data_type_code": "2003", "data_type_alias": "Entertainment"},
    "156": {"original_data_alias": "BROWSER", "data_type_code": "2004", "data_type_alias": "Tool"},
    "157": {"original_data_alias": "APP_MARKET", "data_type_code": "2004", "data_type_alias": "Tool"},
    "159": {"original_data_alias": "KNOWLEDGE", "data_type_code": "159", "data_type_alias": "KNOWLEDGE"},
    "162": {"original_data_alias": "INFOSERV", "data_type_code": "138", "data_type_alias": "News"},
    "201": {"original_data_alias": "WEBMGMT", "data_type_code": "201", "data_type_alias": "WEBMGMT"},
    "202": {"original_data_alias": "DOMAIN", "data_type_code": "202", "data_type_alias": "DOMAIN"},
    "204": {"original_data_alias": "LONLAT", "data_type_code": "204", "data_type_alias": "LONLAT"},
    "205": {"original_data_alias": "MONITOR", "data_type_code": "205", "data_type_alias": "MONITOR"},
    "206": {"original_data_alias": "ACCOUNTLOG", "data_type_code": "206", "data_type_alias": "ACCOUNTLOG"},
    "207": {"original_data_alias": "GUIDLINE", "data_type_code": "207", "data_type_alias": "GUIDLINE"},
    "208": {"original_data_alias": "ACCOUNTDETECT", "data_type_code": "208", "data_type_alias": "ACCOUNTDETECT"},
    "570": {"original_data_alias": "BASEWIFI", "data_type_code": "999", "data_type_alias": "Other"},
    "881": {"original_data_alias": "CUSTOMEXT", "data_type_code": "881", "data_type_alias": "CUSTOMEXT"},
    "882": {"original_data_alias": "INTALLREC", "data_type_code": "882", "data_type_alias": "INTALLREC"},
    "900": {"original_data_alias": "USLVLOG", "data_type_code": "900", "data_type_alias": "USLVLOG"},
    "930": {"original_data_alias": "CARRENTAL", "data_type_code": "930", "data_type_alias": "CARRENTAL"},
    "931": {"original_data_alias": "TAXI", "data_type_code": "931", "data_type_alias": "TAXI"},
    "932": {"original_data_alias": "SHAREBIKE", "data_type_code": "932", "data_type_alias": "SHAREBIKE"},
    "933": {"original_data_alias": "WEBID", "data_type_code": "999", "data_type_alias": "Other"},
    "934": {"original_data_alias": "PENET", "data_type_code": "934", "data_type_alias": "PENET"},
    "935": {"original_data_alias": "ONLINEMEETING", "data_type_code": "935", "data_type_alias": "ONLINEMEETING"},
    "936": {"original_data_alias": "HOBBY", "data_type_code": "936", "data_type_alias": "HOBBY"},
    "938": {"original_data_alias": "APPAVATAR", "data_type_code": "938", "data_type_alias": "APPAVATAR"},
    "939": {"original_data_alias": "POS", "data_type_code": "939", "data_type_alias": "POS"},
    "940": {"original_data_alias": "CATPOOL", "data_type_code": "940", "data_type_alias": "CATPOOL"},
    "941": {"original_data_alias": "CARNET", "data_type_code": "941", "data_type_alias": "CARNET"},
    "942": {"original_data_alias": "GROUPCTRL", "data_type_code": "942", "data_type_alias": "GROUPCTRL"},
    "943": {"original_data_alias": "SECDIAL", "data_type_code": "943", "data_type_alias": "SECDIAL"},
    "944": {"original_data_alias": "GOVSERV", "data_type_code": "138", "data_type_alias": "News"},
    "945": {"original_data_alias": "SAFETY", "data_type_code": "2004", "data_type_alias": "Tool"},
    "946": {"original_data_alias": "VM", "data_type_code": "2004", "data_type_alias": "Tool"},
    "949": {"original_data_alias": "HACKERTOOLS", "data_type_code": "2004", "data_type_alias": "Tool"},
    "950": {"original_data_alias": "VPS", "data_type_code": "950", "data_type_alias": "VPS"},
    "951": {"original_data_alias": "LOAN", "data_type_code": "951", "data_type_alias": "LOAN"},
    "953": {"original_data_alias": "CALL", "data_type_code": "953", "data_type_alias": "CALL"},
    "954": {"original_data_alias": "WEBARREST", "data_type_code": "954", "data_type_alias": "WEBARREST"},
    "955": {"original_data_alias": "APPID", "data_type_code": "955", "data_type_alias": "APPID"},
    "956": {"original_data_alias": "INTELCTRL", "data_type_code": "956", "data_type_alias": "INTELCTRL"},
    "957": {"original_data_alias": "MUSICWEB", "data_type_code": "957", "data_type_alias": "MUSICWEB"},
    "958": {"original_data_alias": "WIFISHARE", "data_type_code": "958", "data_type_alias": "WIFISHARE"},
    "959": {"original_data_alias": "RELIGION", "data_type_code": "119", "data_type_alias": "SNS"},
    "961": {"original_data_alias": "SHARES", "data_type_code": "961", "data_type_alias": "SHARES"},
    "963": {"original_data_alias": "Finance", "data_type_code": "963", "data_type_alias": "Finance"},
    "964": {"original_data_alias": "FUTURES", "data_type_code": "964", "data_type_alias": "FUTURES"},
    "967": {"original_data_alias": "VCURRENCY", "data_type_code": "967", "data_type_alias": "VCURRENCY"},
    "970": {"original_data_alias": "INSURANCE", "data_type_code": "970", "data_type_alias": "INSURANCE"},
    "971": {"original_data_alias": "BUSINESS", "data_type_code": "971", "data_type_alias": "BUSINESS"},
    "972": {"original_data_alias": "ASSET", "data_type_code": "972", "data_type_alias": "ASSET"},
    "973": {"original_data_alias": "INVEST", "data_type_code": "973", "data_type_alias": "INVEST"},
    "974": {"original_data_alias": "DIGCURRENCY", "data_type_code": "963", "data_type_alias": "Finance"},
    "975": {"original_data_alias": "IOT", "data_type_code": "2004", "data_type_alias": "Tool"},
    "976": {"original_data_alias": "SOFTINSTALL", "data_type_code": "2004", "data_type_alias": "Tool"},
    "977": {"original_data_alias": "VISITIP", "data_type_code": "977", "data_type_alias": "VISITIP"},
    "978": {"original_data_alias": "INPUTMETHOD", "data_type_code": "2004", "data_type_alias": "Tool"},
    "979": {"original_data_alias": "HOBYHORSE", "data_type_code": "979", "data_type_alias": "HOBYHORSE"},
    "980": {"original_data_alias": "ENCRYPTION", "data_type_code": "980", "data_type_alias": "ENCRYPTION"},
    "982": {"original_data_alias": "EDUWEB", "data_type_code": "982", "data_type_alias": "EDUWEB"},
    "983": {"original_data_alias": "HOMESERV", "data_type_code": "983", "data_type_alias": "HOMESERV"},
    "984": {"original_data_alias": "MEDICALWEB", "data_type_code": "984", "data_type_alias": "MEDICALWEB"},
    "985": {"original_data_alias": "TAXUTERM", "data_type_code": "985", "data_type_alias": "TAXUTERM"},
    "988": {"original_data_alias": "DNS", "data_type_code": "988", "data_type_alias": "DNS"},
    "989": {"original_data_alias": "RAWPACKET", "data_type_code": "989", "data_type_alias": "RAWPACKET"},
    "990": {"original_data_alias": "RADIUS", "data_type_code": "990", "data_type_alias": "RADIUS"},
    "991": {"original_data_alias": "KEYAPP", "data_type_code": "119", "data_type_alias": "SNS"},
    "994": {"original_data_alias": "UAV", "data_type_code": "994", "data_type_alias": "UAV"},
    "995": {"original_data_alias": "GPS", "data_type_code": "995", "data_type_alias": "GPS"},
    "998": {"original_data_alias": "DBOP", "data_type_code": "998", "data_type_alias": "DBOP"},
    "999": {"original_data_alias": "Other", "data_type_code": "999", "data_type_alias": "Other"},
    "200": {"original_data_alias": "SMS", "data_type_code": "200", "data_type_alias": "SMS"},
    "109": {"original_data_alias": "Call", "data_type_code": "109", "data_type_alias": "Call"},
    "210": {"original_data_alias": "Fax", "data_type_code": "210", "data_type_alias": "Fax"},
    "2101": {"original_data_alias": "Fixed Radius", "data_type_code": "2101", "data_type_alias": "Fixed Radius"},
    "2201": {"original_data_alias": "Mobile Radius", "data_type_code": "2201", "data_type_alias": "Mobile Radius"},
    # 由于ODS层Radius没有协议类型。所以之类用认证类型替换
    "1020001": {"original_data_alias": "Fixed Radius", "data_type_code": "2101", "data_type_alias": "Fixed Radius"},
    "1020004": {"original_data_alias": "Mobile Radius", "data_type_code": "2201", "data_type_alias": "Mobile Radius"},

    "2202": {"original_data_alias": "MobileNetwork", "data_type_code": "2202", "data_type_alias": "MobileNetwork"},
    "1201": {"original_data_alias": "URL", "data_type_code": "1201", "data_type_alias": "URL"},
    "1202": {"original_data_alias": "Mail", "data_type_101code": "1202", "data_type_alias": "Mail"},
    "1203": {"original_data_alias": "Chat", "data_type_code": "1203", "data_type_alias": "Chat"},
    "1204": {"original_data_alias": "BBS_Weibo", "data_type_code": "1204", "data_type_alias": "BBS_Weibo"},
    "1205": {"original_data_alias": "Other_Log", "data_type_code": "1205", "data_type_alias": "Other_Log"},
    "2001": {"original_data_alias": "Travel", "data_type_code": "2001", "data_type_alias": "Travel"},
    "2002": {"original_data_alias": "Multimedia", "data_type_code": "2002", "data_type_alias": "Multimedia"},
    "2003": {"original_data_alias": "Entertainment", "data_type_code": "2003", "data_type_alias": "Entertainment"},
    "2004": {"original_data_alias": "Tool", "data_type_code": "2004", "data_type_alias": "Tool"}
}
# 局点映射
uparea_mapping = {
    "210213": "Alger",
    "220214": "Annaba",
    "230215": "Oran"
}
# 运营商映射-需要再Radius关联率的时候做映射
operator_mapping = {
    "5": "Ooredoo",
    "6": "Mobilis",
    "7": "Djezzy",
    "99": "Autre"
}

resource_data_type = {
    "deye_v64_fixednetradius": "Fixed Radius",
    "deye_v64_mobilenetradius_djezzy": "Mobile Radius",
    "deye_v64_mobilenetradius_mobilis": "Mobile Radius",
    "deye_v64_mobilenetradius_ooredoo": "Mobile Radius",
    "deye_v64_call": "Call",
    "deye_v64_sms": "SMS",
    "deye_v64_fax": "Fax",
    "deye_v64_voip": "AppCall",
    "deye_v64_http": "HTTP",
    "deye_v64_email": "Email",
    "deye_v64_engine": "Engine",
    "deye_v64_ftp": "FTP",
    "deye_v64_im": "IM",
    "deye_v64_news": "News",
    "deye_v64_other": "Other",
    "deye_v64_remotectrl": "RemoteCTRL",
    "deye_v64_location_djezzy": "MobileNetwork",
    "deye_v64_location_ooredoo": "MobileNetwork",
    "deye_v64_location_mobilis": "MobileNetwork",
    "deye_v64_sns": "SNS",
    "deye_v64_telnet": "Telnet",
    "deye_v64_travel": "Travel",
    "deye_v64_vpn": "VPN",
    "deye_v64_tool": "Tool",
    "deye_v64_terminal": "Terminal",
    "deye_v64_lbs": "Location",
    "deye_v64_entertainment": "Entertainment",
    "deye_v64_shopping": "Shopping",
    "deye_v64_multimedia": "Multimedia",
    "deye_v64_finance": "Finance",
    # 全息档案的映射
    "deye_v64_quanxidangan_phone": "Phone",
    "deye_v64_quanxidangan_email": "Email",
    "deye_v64_quanxidangan_radius": "Fixed RADIUS",
    "deye_v64_quanxidangan_fix_ip": "Fixed IP",
    "deye_v64_quanxidangan_im": "IM",
    "deye_v64_quanxidangan_app": "Application",
    "deye_v64_quanxidangan_website": "Website",
}
