import unittest
import json
import subprocess
from unittest.mock import patch, MagicMock
import sys
import os
# 添加父目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from start_spark_sql import execSparkCommand
from common_util.mysql_common_util import getProductSql
from common_util.doris_common_util import loadToDoris


class TestSparkSqlMethods(unittest.TestCase):

    @patch('mysql.connector.connect')
    def test_getSparkSql_ValidSqlId_ReturnsSqlAndResourceConf(self, mock_connect):
        mock_cursor = MagicMock()
        mock_connect.return_value.cursor.return_value = mock_cursor
        mock_cursor.fetchall.return_value = [(
            'SELECT * FROM table WHERE t.insert_day BETWEEN \"{captureStartDay}\" AND \"{captureEndDay}\"',
            '{\"spark.driver.memory\":\"4g\",\"spark.driver.cores\":\"4\",\"spark.executor.memory\":\"8G\",\"spark.executor.instances\":\"8\",\"spark.executor.cores\":\"4\",\"spark.default.parallelism\":\"32\"}')]

        mysql_config = json.dumps(
            {"user": "u", "password": "password", "host": "ip", "database": "ads_ops", "raise_on_warnings": "true"})
        result = getProductSql(1, mysql_config)

        self.assertEqual(result, (
            'SELECT * FROM table WHERE t.insert_day BETWEEN \"{captureStartDay}\" AND \"{captureEndDay}\"',
            '{\"spark.driver.memory\":\"4g\",\"spark.driver.cores\":\"4\",\"spark.executor.memory\":\"8G\",\"spark.executor.instances\":\"8\",\"spark.executor.cores\":\"4\",\"spark.default.parallelism\":\"32\"}'))
        mock_cursor.close.assert_called_once()
        mock_connect.return_value.close.assert_called_once()

    @patch('mysql.connector.connect')
    def test_getSparkSql_InvalidSqlId_RaisesValueError(self, mock_connect):
        mock_cursor = MagicMock()
        mock_connect.return_value.cursor.return_value = mock_cursor
        mock_cursor.fetchall.return_value = []

        mysql_config = json.dumps(
            {"user": "u", "password": "password", "host": "ip", "database": "ads_ops", "raise_on_warnings": "true"})

        with self.assertRaises(ValueError) as context:
            getProductSql(999, mysql_config)

        self.assertTrue("该sql_id找不到对应sql配置项" in str(context.exception))
        mock_cursor.close.assert_called_once()
        mock_connect.return_value.close.assert_called_once()

    @patch('subprocess.run')
    def test_execSparkCommand_ValidCommand_ReturnsOutput(self, mock_run):
        mock_run.return_value = MagicMock(stdout="Query Result")

        sparkSql = ("SELECT * FROM table WHERE t.insert_day BETWEEN \"{captureStartDay}\" AND \"{captureEndDay}\"",
                    '{\"spark.driver.memory\":\"4g\",\"spark.driver.cores\":\"4\",\"spark.executor.memory\":\"8G\",\"spark.executor.instances\":\"8\",\"spark.executor.cores\":\"4\",\"spark.default.parallelism\":\"32\"}')
        result = execSparkCommand(sparkSql, "task", "remote_server", "2023-01-01", "2023-01-02", "", "")

        self.assertEqual(result, "Query Result")
        mock_run.assert_called_once()

    @patch('subprocess.run')
    def test_execSparkCommand_CommandExecutionFails_PrintsError(self, mock_run):
        mock_run.side_effect = subprocess.CalledProcessError(1, "command", stderr="Execution error")

        sparkSql = ("SELECT * FROM table WHERE t.insert_day BETWEEN \"{captureStartDay}\" AND \"{captureEndDay}\"",
                    '{\"spark.driver.memory\":\"4g\",\"spark.driver.cores\":\"4\",\"spark.executor.memory\":\"8G\",\"spark.executor.instances\":\"8\",\"spark.executor.cores\":\"4\",\"spark.default.parallelism\":\"32\"}')
        result = execSparkCommand(sparkSql, "task", "remote_server", "2023-01-01", "2023-01-02", "", "")

        self.assertIsNone(result)
        mock_run.assert_called_once()

    @patch('common_util.doris_common_util.DorisStreamLoader')
    def test_loadToDoris_ValidData_LoadsDataToDoris(self, mock_loader):
        mock_loader_instance = mock_loader.return_value
        mock_loader_instance.load_json.return_value = None

        outputs = """1205	230215	ODS	2025-03-10	06	1023945
1205	220214	ODS	2025-03-10	15	1258614
1205	230215	ODS	2025-03-10	03	1500086"""
        doris_conf = json.dumps(
            {"host": "localhost", "port": 8030, "database": "db", "username": "user", "password": "password"})

        loadToDoris(outputs, "target_table", "col1,col2", doris_conf)

        mock_loader.assert_called_once_with("localhost", 8030, "db", "target_table")
        mock_loader_instance.load_json.assert_called_once()

    @patch('common_util.doris_common_util.DorisStreamLoader')
    def test_loadToDoris_EmptyOutput_ReturnsImmediately(self, mock_loader):
        doris_conf = json.dumps(
            {"host": "localhost", "port": 8030, "database": "db", "username": "user", "password": "password"})

        loadToDoris("", "target_table", "data_type,uparea_alias,data_layer,capture_day,capture_hour,num,create_time",
                    doris_conf)

        mock_loader.assert_not_called()

    @patch('common_util.doris_common_util.DorisStreamLoader')
    def test_loadToDoris_DataLoadingFails_PrintsError(self, mock_loader):
        mock_loader_instance = mock_loader.return_value
        mock_loader_instance.load_json.side_effect = Exception("Loading error")

        outputs = """1205	230215	ODS	2025-03-10	06	1023945
1205	220214	ODS	2025-03-10	15	1258614
1205	230215	ODS	2025-03-10	03	1500086"""
        doris_conf = json.dumps(
            {"host": "localhost", "port": 8030, "database": "db", "username": "user", "password": "password"})

        loadToDoris(outputs, "target_table",
                    "data_type,uparea_alias,data_layer,capture_day,capture_hour,num,create_time", doris_conf)

        mock_loader.assert_called_once()
        mock_loader_instance.load_json.assert_called_once()


if __name__ == '__main__':
    unittest.main()
