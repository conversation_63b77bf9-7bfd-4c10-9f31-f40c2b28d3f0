import json
import unittest
from unittest.mock import Magic<PERSON><PERSON>, Mock, patch, ANY
import time

import sys
import os

# 添加父目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from common_util.es_common_util import get_ads_count, process_resource_data, calculate_diff_ratio, get_es_hbase_diff, \
    scroll_query, \
    get_other_data_list, get_radius_data_list, generate_es_index_suffixes, get_timestamp, filter_ips, get_archive_data


class TestEsCommonUtil(unittest.TestCase):
    @patch('common_util.es_common_util.ElasticSearchOperator')
    @patch('common_util.es_common_util.HbaseConnect')
    def test_get_ads_count(self, mock_hbase, mock_es):
        es_conf = {"host": "localhost", "port": 9200}
        dsl = ["{captureStartDay}", "{captureEndDay}"]
        resource_names = "deye_v64_call,deye_v64_sms"
        captureStartDay = "2023-01-01"
        captureEndDay = "2023-01-02"
        mock_es_instance = mock_es.return_value
        mock_es_instance.exec_query.return_value = json.dumps({"aggregations": {
            "data_type": {"doc_count_error_upper_bound": 0, "sum_other_doc_count": 0, "buckets": [
                {"key": "109", "doc_count": 3783,
                 "capture_day": {"doc_count_error_upper_bound": 0, "sum_other_doc_count": 0, "buckets": [
                     {"key": "2025-03-18", "doc_count": 3783,
                      "uparea_alias": {"doc_count_error_upper_bound": 0, "sum_other_doc_count": 0,
                                       "buckets": []}}]}}]}}})
        result = get_ads_count(es_conf, dsl, resource_names, captureStartDay, captureEndDay)
        self.assertIsNotNone(result)

    @patch('common_util.es_common_util.ElasticSearchOperator')
    @patch('common_util.es_common_util.HbaseConnect')
    def test_process_resource_data(self, mock_hbase, mock_es):
        resource_name = "deye_v64_email"
        dsl = "{captureStartTime}"
        es_operator = mock_es.return_value
        hbase_conn = mock_hbase.return_value
        captureStartDay = "2023-01-01"
        captureEndDay = "2023-01-02"
        captureStartHour = "00"
        sample_size = 2
        uparea_id = "210213"
        es_operator.exec_scroll_query.return_value = json.dumps({"hits": {"hits": [{"_id": "1"}, {"_id": "2"}]}})
        hbase_conn.get_rows_count.return_value = 50
        ids, data = process_resource_data(resource_name, dsl, es_operator, hbase_conn, captureStartDay, captureEndDay,
                                          captureStartHour, sample_size, uparea_id)
        self.assertIsNotNone(ids)
        self.assertIsNotNone(data)

    def test_calculate_diff_ratio(self):
        es_count = 100
        hbase_count = 50
        result = calculate_diff_ratio(es_count, hbase_count)
        self.assertEqual(result, 0.5)

    @patch('common_util.es_common_util.ElasticSearchOperator')
    @patch('common_util.es_common_util.HbaseConnect')
    def test_get_es_hbase_diff(self, mock_hbase, mock_es):
        task_conf = {"es_conf": {"host": "localhost", "port": 9200}, "hbase_conf": {"host": "localhost", "port": 9090}}
        dsl = ["{captureStartTime}", json.dumps({"sample_size": 2})]
        resource_names = "deye_v64_call,deye_v64_sms"
        captureStartDay = "2023-01-01"
        captureEndDay = "2023-01-02"
        captureStartHour = "00"
        captureEndHour = "23"
        mock_es_instance = mock_es.return_value
        mock_es_instance.exec_scroll_query.return_value = json.dumps({"hits": {"hits": [{"_id": "1"}, {"_id": "2"}]}})
        mock_hbase_instance = mock_hbase.return_value
        mock_hbase_instance.get_rows_count.return_value = 50
        result = get_es_hbase_diff(task_conf, dsl, resource_names, captureStartDay, captureEndDay, captureStartHour,
                                   captureEndHour)
        self.assertIsNotNone(result)
        for r in result:
            self.assertIn(r["data_type"], ["Call", "SMS"])
            self.assertEqual(r["diff_num"], calculate_diff_ratio(2, 50))

    def test_scroll_query(self):
        ids = []
        sample_size = 100
        result_json = json.dumps({"hits": {"hits": [{"_id": "1"}, {"_id": "2"}]}, "_scroll_id": "scroll_id"})
        es_operator = Mock()
        es_operator.exec_scroll_query.return_value = json.dumps({"hits": {"hits": []}})
        result = scroll_query(ids, sample_size, json.loads(result_json), es_operator)
        self.assertIsNotNone(result)

    def test_get_other_data_list(self):
        result = json.dumps({"aggregations": {"data_type": {"buckets": [{"key": "100", "capture_day": {
            "buckets": [
                {"key": "2023-01-01", "uparea_alias": {"buckets": [{"key": "Alger", "doc_count": 100}]}}]}}]}}})
        result_list = get_other_data_list(result)
        self.assertIsNotNone(result_list)
        self.assertEqual(result_list[0]["data_type"], "HTTP")
        self.assertEqual(result_list[0]["uparea_alias"], "Alger")
        self.assertEqual(result_list[0]["capture_day"], "2023-01-01")
        self.assertEqual(result_list[0]["num"], 100)

    def test_get_radius_data_list(self):
        result = json.dumps({"aggregations": {
            "data_type": {
                "doc_count_error_upper_bound": 0,
                "sum_other_doc_count": 0,
                "buckets": [
                    {
                        "key": "2201",
                        "doc_count": 58414116,
                        "capture_day": {
                            "doc_count_error_upper_bound": 0,
                            "sum_other_doc_count": 0,
                            "buckets": [
                                {
                                    "key": "2025-04-10",
                                    "doc_count": 58414116,
                                    "operator": {
                                        "doc_count_error_upper_bound": 0,
                                        "sum_other_doc_count": 0,
                                        "buckets": [
                                            {
                                                "key": "6",
                                                "doc_count": 38924646
                                            },
                                            {
                                                "key": "",
                                                "doc_count": 19489440
                                            },
                                            {
                                                "key": "5",
                                                "doc_count": 19
                                            },
                                            {
                                                "key": "7",
                                                "doc_count": 11
                                            }
                                        ]
                                    }
                                }
                            ]
                        }
                    },
                    {
                        "key": "2101",
                        "doc_count": 180,
                        "capture_day": {
                            "doc_count_error_upper_bound": 0,
                            "sum_other_doc_count": 0,
                            "buckets": [
                                {
                                    "key": "2025-04-10",
                                    "doc_count": 180,
                                    "operator": {
                                        "doc_count_error_upper_bound": 0,
                                        "sum_other_doc_count": 0,
                                        "buckets": []
                                    }
                                }
                            ]
                        }
                    }
                ]
            }
        }})
        result_list = get_radius_data_list(result)
        self.assertIsNotNone(result_list)
        self.assertEqual(result_list[0]["data_type"], "Mobile Radius")
        # self.assertEqual(result_list[0]["operator"], "Autre")
        self.assertEqual(result_list[0]["capture_day"], "2025-04-10")
        self.assertEqual(result_list[0]["num"], 38924646)

    def test_generate_es_index_suffixes(self):
        indexs = "index1,index2"
        captureStartDay = "2023-01-01"
        captureEndDay = "2023-01-02"
        result = generate_es_index_suffixes(indexs, captureStartDay, captureEndDay)
        self.assertIsNotNone(result)
        self.assertEqual(result, {"index1_202301", "index2_202301"})

    def test_get_timestamp(self):
        date_str = "2023-01-01"
        hour_str = "00"
        result = get_timestamp(date_str, hour_str)
        self.assertIsNotNone(result)

    def test_valid_ipv4(self):
        """测试标准IPv4格式校验"""
        input_ips = ["***********", "********", "***************"]
        expected = ["***********", "********", "***************"]
        self.assertEqual(filter_ips(input_ips), expected)

    def test_valid_ipv6(self):
        """测试标准IPv6格式及缩写校验"""
        input_ips = [
            "2001:0db8:85a3:0000:0000:8a2e:0370:7334",
            "2001:db8::1",
            "FE80::1FF:FE23:4567:890A"
        ]
        expected = [
            "2001:0db8:85a3:0000:0000:8a2e:0370:7334",
            "2001:db8::1",
            "FE80::1FF:FE23:4567:890A"
        ]
        print(f"过滤后的输出结果为{filter_ips(input_ips)}")
        self.assertEqual(filter_ips(input_ips), expected)

    def test_mixed_valid_invalid(self):
        """测试混合有效/无效IP过滤"""
        input_ips = ["***********", "not.an.ip", "::1", "192.168.1.256"]
        expected = ["***********", "::1"]
        print(f"过滤后的输出结果为{filter_ips(input_ips)}")
        self.assertEqual(expected, filter_ips(input_ips))

    def test_invalid_formats(self):
        """测试各类非法格式"""
        invalid_ips = [
            "256.1.1.1",
            "fe80::1%eth0",
            "************",
            "2001::g::1",
            "localhost"
        ]
        self.assertEqual([], filter_ips(invalid_ips))

    def test_edge_cases(self):
        """测试边界条件处理"""
        # 空列表测试
        self.assertEqual(filter_ips([]), [])
        # 带空白字符测试。支持带空格的ip地址。
        self.assertEqual(["***********", "::1"], filter_ips(["***********", "::1"]))
        # 0值测试。::是一个合法的地址
        self.assertEqual(["0.0.0.0", "::"], filter_ips(["0.0.0.0", "::"]))

    def test_performance(self):
        """测试大数据量处理能力"""
        # 修正为生成合法的IP地址范围（0-255）
        large_list = [f"192.168.1.{i}" for i in range(256)] * 4  # 生成1024个合法地址
        start_time = time.perf_counter()
        result = filter_ips(large_list)
        elapsed = time.perf_counter() - start_time

        self.assertEqual(len(result), 1024)
        self.assertLess(elapsed, 1.0, "处理1000+条数据超时")  # 假设1秒为合理阈值


class TestGetArchiveData(unittest.TestCase):
    """针对get_archive_data函数的测试套件"""

    def setUp(self):
        self.es_conf = {}
        self.test_dsl = ["{query_str} AND @timestamp:[{captureStartDay} TO {captureEndDay}]"]
        self.start_day = "2025-03-31"
        self.end_day = "2025-04-01"

    # def tearDown(self):
    #     """清理mock对象"""
    #     self.time_patcher.stop()
    #     self.datetime_patcher.stop()

    def test_fixed_ip_filter(self):
        """测试固定IP过滤逻辑"""
        with patch('common_util.es_common_util.filter_ips') as mock_filter, \
                patch('common_util.es_common_util.ElasticSearchOperator.exec_query') as mock_es_result, \
                patch.dict('common_util.data_type_mapping.resource_data_type', {'test_index': 'Fixed IP'}):
            # 模拟filter_ips返回
            mock_filter.return_value = ["***********"]
            mock_es_result.return_value = "{\"took\":10,\"timed_out\":false,\"_shards\":{\"total\":100,\"successful\":100,\"skipped\":0,\"failed\":0},\"hits\":{\"total\":{\"value\":45,\"relation\":\"eq\"},\"max_score\":7.4269457,\"hits\":[{\"_index\":\"deye_v64_quanxidangan_website\",\"_type\":\"_doc\",\"_id\":\"45da0cf325e061b5408ebd120c65cdab\",\"_score\":7.4269457,\"_source\":{\"update_time\":1726832250000,\"sort_score\":0,\"latest_relation_time\":1743546590000,\"create_time\":1726832250000,\"archive_name\":\"party-service-prod.ak.epicgames.com\",\"archive_type\":3,\"id\":\"45da0cf325e061b5408ebd120c65cdab\",\"file_flag\":0,\"behavior_num\":0,\"latest_relation_ip\":\"*************\",\"latest_relation_country\":\"États-Unis\"}},{\"_index\":\"deye_v64_quanxidangan_website\",\"_type\":\"_doc\",\"_id\":\"cf0aa9e16c58230030ec616fadb1082d\",\"_score\":7.3520136,\"_source\":{\"update_time\":1726864643000,\"sort_score\":0,\"latest_relation_time\":1743548523000,\"create_time\":1726864643000,\"archive_name\":\"fun.biugames.com\",\"archive_type\":3,\"id\":\"cf0aa9e16c58230030ec616fadb1082d\",\"file_flag\":0,\"behavior_num\":0,\"latest_relation_ip\":\"************\",\"latest_relation_country\":\"États-Unis\"}},{\"_index\":\"deye_v64_quanxidangan_website\",\"_type\":\"_doc\",\"_id\":\"efac00827831098919717a2243f06efb\",\"_score\":7.350731,\"_source\":{\"update_time\":1727043332000,\"sort_score\":0,\"latest_relation_time\":1743540888000,\"create_time\":1727043332000,\"archive_name\":\"243408.selcdn.ru\",\"archive_type\":3,\"id\":\"efac00827831098919717a2243f06efb\",\"file_flag\":0,\"behavior_num\":0,\"latest_relation_ip\":\"***********\",\"latest_relation_country\":\"Russie\"}},{\"_index\":\"deye_v64_quanxidangan_website\",\"_type\":\"_doc\",\"_id\":\"655b3dd6a69dfd788cd16ba3d8db3bbe\",\"_score\":7.3503027,\"_source\":{\"update_time\":1726832250000,\"sort_score\":0,\"latest_relation_time\":1743550965000,\"create_time\":1726832250000,\"archive_name\":\"updatev2.easy4ipcloud.com\",\"archive_type\":3,\"id\":\"655b3dd6a69dfd788cd16ba3d8db3bbe\",\"file_flag\":0,\"behavior_num\":0,\"latest_relation_ip\":\"**************\",\"latest_relation_country\":\"États-Unis\"}},{\"_index\":\"deye_v64_quanxidangan_website\",\"_type\":\"_doc\",\"_id\":\"675afa1c324fa638bf1722f1963b4645\",\"_score\":7.3391047,\"_source\":{\"update_time\":1727022365000,\"sort_score\":0,\"latest_relation_time\":1743542520000,\"create_time\":1727022365000,\"archive_name\":\"guardaserie-org.disqus.com\",\"archive_type\":3,\"id\":\"675afa1c324fa638bf1722f1963b4645\",\"file_flag\":0,\"behavior_num\":0,\"latest_relation_ip\":\"**************\",\"latest_relation_country\":\"Suède\"}},{\"_index\":\"deye_v64_quanxidangan_website\",\"_type\":\"_doc\",\"_id\":\"af8476da379baeddf2957ef1517b42d8\",\"_score\":7.3391047,\"_source\":{\"update_time\":1726864643000,\"sort_score\":0,\"latest_relation_time\":1743544935000,\"create_time\":1726864643000,\"archive_name\":\"user-settings.s3.amazonaws.com\",\"archive_type\":3,\"id\":\"af8476da379baeddf2957ef1517b42d8\",\"file_flag\":0,\"behavior_num\":0,\"latest_relation_ip\":\"*************\",\"latest_relation_country\":\"États-Unis\"}},{\"_index\":\"deye_v64_quanxidangan_website\",\"_type\":\"_doc\",\"_id\":\"ba9c0e31c90353b39fcb87eeaef79771\",\"_score\":7.3382387,\"_source\":{\"update_time\":1726832250000,\"sort_score\":0,\"latest_relation_time\":1743550655000,\"create_time\":1726832250000,\"archive_name\":\"www.mexcsensors.com\",\"archive_type\":3,\"id\":\"ba9c0e31c90353b39fcb87eeaef79771\",\"file_flag\":0,\"behavior_num\":0,\"latest_relation_ip\":\"************\",\"latest_relation_country\":\"Japon\",\"latest_relation_city\":\"Tokyo\"}},{\"_index\":\"deye_v64_quanxidangan_website\",\"_type\":\"_doc\",\"_id\":\"e2203e31054c83da5302c65793fbfbff\",\"_score\":7.337371,\"_source\":{\"update_time\":1726832250000,\"sort_score\":0,\"latest_relation_time\":1743545113000,\"create_time\":1726832250000,\"archive_name\":\"webcast-ws16-normal-useast5.tiktokv.us\",\"archive_type\":3,\"id\":\"e2203e31054c83da5302c65793fbfbff\",\"file_flag\":0,\"behavior_num\":0,\"latest_relation_ip\":\"*************\",\"latest_relation_country\":\"États-Unis\"}},{\"_index\":\"deye_v64_quanxidangan_website\",\"_type\":\"_doc\",\"_id\":\"0de95155f982c795a4329d65bb0863ef\",\"_score\":7.336068,\"_source\":{\"update_time\":1726832250000,\"sort_score\":0,\"latest_relation_time\":1743544463000,\"create_time\":1726832250000,\"archive_name\":\"analytics.valiuz.com\",\"archive_type\":3,\"id\":\"0de95155f982c795a4329d65bb0863ef\",\"file_flag\":0,\"behavior_num\":0,\"latest_relation_ip\":\"*************\",\"latest_relation_country\":\"France\"}},{\"_index\":\"deye_v64_quanxidangan_website\",\"_type\":\"_doc\",\"_id\":\"f2f35f9f9e369c652e393271daaafbce\",\"_score\":7.3351994,\"_source\":{\"update_time\":1726832250000,\"sort_score\":0,\"latest_relation_time\":1743540001000,\"create_time\":1726832250000,\"archive_name\":\"mediacdn.ttzgame.com\",\"archive_type\":3,\"id\":\"f2f35f9f9e369c652e393271daaafbce\",\"file_flag\":0,\"behavior_num\":0,\"latest_relation_ip\":\"*************\",\"latest_relation_country\":\"États-Unis\"}}]}}"
            # 执行测试
            stats, details = get_archive_data(
                es_conf=self.es_conf,
                dsl=self.test_dsl,
                index_name="test_index",
                archive_datas=[{"archive_name": "***********", "app_type": "other"}],
                captureStartDay=self.start_day,
                captureEndDay=self.end_day
            )

            detail_expect = [
                {'data_type': 'Fixed IP', 'capture_day': '2025-03-31',
                 'app_type': 'other', 'missing_doc_account': '***********'}]
            print(stats)
            print(details)
            # 验证结果必须是最后目标结果.考虑时间戳需要删除create_time后比较
            [i.pop('create_time', None) for i in stats]
            [i.pop('create_time', None) for i in details]
            self.assertEqual(
                [{'data_type': 'Fixed IP', 'capture_day': '2025-03-31', 'actual_number': 45, 'theoretical_number': 1}],
                stats)
            self.assertEqual(detail_expect, details)

    def test_empty_archive_datas(self):
        """测试archive_datas参数为空的情况"""
        # 使用mock模拟依赖
        with patch('common_util.es_common_util.ElasticSearchOperator') as mock_es_class, \
                patch('common_util.es_common_util.process_archive_data') as mock_process, \
                patch.dict('common_util.data_type_mapping.resource_data_type', {'test_index': 'Fixed IP'}):
            # 设置mock的返回值
            mock_es_instance = mock_es_class.return_value
            mock_process.return_value = ([{'data_type': 'Fixed IP', 'capture_day': self.start_day,
                                           'actual_number': 0, 'theoretical_number': 0,
                                           'create_time': 1717266880000}], [])

            # 测试空列表情况
            stats1, details1 = get_archive_data(
                es_conf=self.es_conf,
                dsl=self.test_dsl,
                index_name="test_index",
                archive_datas=[],  # 空列表
                captureStartDay=self.start_day,
                captureEndDay=self.end_day
            )

            # 测试None情况
            stats2, details2 = get_archive_data(
                es_conf=self.es_conf,
                dsl=self.test_dsl,
                index_name="test_index",
                archive_datas=None,  # None
                captureStartDay=self.start_day,
                captureEndDay=self.end_day
            )

            # 验证process_archive_data被正确调用
            # 第一次调用 - 空列表情况
            mock_process.assert_any_call(
                "test_index", [], mock_es_instance, self.test_dsl, self.start_day,
                ANY  # start_time是动态生成的时间戳，使用ANY匹配
            )

            # 第二次调用 - None情况
            mock_process.assert_any_call(
                "test_index", [], mock_es_instance, self.test_dsl, self.start_day,
                ANY  # start_time是动态生成的时间戳，使用ANY匹配
            )
            print(f"stats2=>{stats2}", f"details2=>{details2}")
            # 验证返回结果
            self.assertEqual(stats1[0]['theoretical_number'], 0)
            self.assertEqual(stats1[0]['actual_number'], 0)
            self.assertEqual(len(details1), 0)

            self.assertEqual(stats2[0]['theoretical_number'], 0)
            self.assertEqual(stats2[0]['actual_number'], 0)
            self.assertEqual(len(details2), 0)

    def test_batch_processing(self):
        """测试分批次查询逻辑"""
        mock_es = MagicMock()
        with patch('common_util.es_common_util.ElasticSearchOperator', return_value=mock_es):
            # 构造1600个测试账号.因为被测试的BATCH=1000
            test_accounts = [f"acc{i}" for i in range(1600)]
            archive_datas = [{
                "archive_name": archive_name,
                "app_type": "QQ"
            }
                for archive_name in test_accounts
            ]
            # 配置ES返回空结果
            mock_es.exec_query.return_value = json.dumps({
                "hits": {"total": {"value": 1000}, "hits": []}
            })

            stats, _ = get_archive_data(
                es_conf=self.es_conf,
                dsl=self.test_dsl,
                index_name="deye_v64_quanxidangan_im",
                archive_datas=archive_datas,
                captureStartDay=self.start_day,
                captureEndDay=self.end_day
            )

            # 验证分2次查询（ceil(600/500)=2）
            self.assertEqual(2, mock_es.exec_query.call_count)
            self.assertEqual(2000, stats[0]["actual_number"])

    def test_normal_case(self):
        """测试正常结果处理"""
        mock_es = MagicMock()
        test_response = {
            "hits": {
                "total": {"value": 1},
                "hits": [
                    {"_index": "deye_v64_quanxidangan_app", "_id": "72cc4cb5d7240880665beeb4a787a367",
                     "_source": {"archive_name": "foxit_cloud", "app_type": "oa"}}
                ]
            }
        }

        with patch('common_util.es_common_util.ElasticSearchOperator', return_value=mock_es), \
                patch('time.time', return_value=1717266880), \
                patch('datetime.datetime') as mock_datetime:
            # 配置时间相关mock
            mock_datetime.now.return_value.strftime.return_value = "15"
            mock_es.exec_query.return_value = json.dumps(test_response)

            stats, details = get_archive_data(
                es_conf=self.es_conf,
                dsl=self.test_dsl,
                index_name="deye_v64_quanxidangan_app",
                archive_datas=[{"archive_name": "foxit_cloud", "app_type": "oa"},
                               {"archive_name": "foxit_cloud", "app_type": "remote_tool"},
                               {"archive_name": "foxit_cloud", "app_type": "QQ"}],
                captureStartDay=self.start_day,
                captureEndDay=self.end_day
            )

            # 验证统计结果
            self.assertEqual(1, stats[0]["actual_number"])  # 3总-2缺失=1实际
            self.assertEqual(2, len(details))
            self.assertEqual("foxit_cloud", details[0]["missing_doc_account"])


if __name__ == '__main__':
    unittest.main()
