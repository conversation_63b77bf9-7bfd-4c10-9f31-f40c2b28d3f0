import unittest
from unittest.mock import patch, MagicMock
import sys
import os
import json
# 添加父目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from common_util.mysql_common_util import getProductSql
from common_util.doris_common_util import loadToDorisForCompleteDataCSV
from unittest.mock import ANY


class TestGetProductSql(unittest.TestCase):
    @patch('mysql.connector.connect')
    def test_getProductSql_ExistingId_ReturnsSqlAndResource(self, mock_connect):
        mock_cursor = MagicMock()
        mock_connect.return_value.cursor.return_value = mock_cursor
        mock_cursor.execute.return_value = None
        mock_cursor.fetchall.return_value = [('SELECT * FROM table', '{"resource": "conf"}')]

        mysql_config = """
        {
            \"host\": \"localhost\",
            \"user\": \"user\",
            \"password\": \"password\",
            \"database\": \"database\"
        }
        """

        result = getProductSql(1, mysql_config)

        self.assertEqual(result, ('SELECT * FROM table', '{"resource": "conf"}'))
        mock_cursor.close.assert_called_once()
        mock_connect.return_value.close.assert_called_once()

    @patch('mysql.connector.connect')
    def test_getProductSql_NonExistingId_ReturnsEmptyResult(self, mock_connect):
        mock_cursor = MagicMock()
        mock_connect.return_value.cursor.return_value = mock_cursor
        mock_cursor.execute.return_value = None
        mock_cursor.fetchall.return_value = []

        mysql_config = """{
            \"host\": \"localhost\",
            \"user\": \"user\",
            \"password\": \"password\",
            \"database\": \"database\"
        }"""

        try:
            result = getProductSql(999, mysql_config)
        except Exception as e:
            print(e)
            self.assertEqual(e.args[0], '该sql_id找不到对应sql配置项，请检查(999,)')
        mock_cursor.close.assert_called_once()
        mock_connect.return_value.close.assert_called_once()

    @patch('common_util.doris_common_util.DorisStreamLoader')
    def test_loadToDorisForCompleteDataCSV_ValidData_LoadsDataToDoris(self, mock_loader):
        mock_loader_instance = mock_loader.return_value
        mock_loader_instance.load_json.return_value = None
        
        # 模拟csv输入数据
        outputs = "2025-04-08\tAutre\tAssociation_1\tFixed\t197.206.167.188\t103\t\t0\t0\t0\t0\t\t197.206.167.188\t0\t18be89b8-0a74-459f-8b56-87e26776823e\t1744088926000\t1744162978930"
        doris_conf = json.dumps({
            "host": "************", 
            "port": 8030, 
            "database": "ads_ops", 
            "username": "root", 
            "password": "123456"
        })
        
        loadToDorisForCompleteDataCSV(outputs, "ops_pr_radius_association_detail", "capture_day,operator,statistics_type,auth_type,radius_ip,data_type,radius_account,radius_start_port,radius_end_port,radius_start_time,radius_end_time,account,strsrc_ip,src_port,data_id,capture_time,create_time", doris_conf)
        
        # 验证最后添加的时间戳字段
        mock_loader_instance.load_json.assert_called_once()
        loaded_data = json.loads(mock_loader_instance.load_json.call_args[0][0])
        self.assertEqual(loaded_data[0].get('create_time'), ANY)  # 验证自动添加时间戳

    @patch('common_util.doris_common_util.DorisStreamLoader')
    def test_loadToDorisForCompleteDataCSV_EmptyOutput_ReturnsImmediately(self, mock_loader):
        doris_conf = json.dumps({
            "host": "************", 
            "port": 8030, 
            "database": "ads_ops", 
            "username": "root", 
            "password": "123456"
        })
        
        loadToDorisForCompleteDataCSV("", "ops_pr_radius_association_detail", "capture_day,operator,statistics_type,auth_type,radius_ip,data_type,radius_account,radius_start_port,radius_end_port,radius_start_time,radius_end_time,account,strsrc_ip,src_port,data_id,capture_time,create_time", doris_conf)
        
        mock_loader.assert_not_called()

if __name__ == '__main__':
    unittest.main()
