import argparse
import unittest
import json
from unittest.mock import patch, MagicMock
import sys
import os
# 添加父目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from common_util.mysql_common_util import getProductSql
from start_radius_association_detail import main

class TestRadiusAssociationDetailMetadata(unittest.TestCase):

    @patch('mysql.connector.connect')
    def test_getSparkSql_ValidSqlId_ReturnsSqlAndResourceConf(self, mock_connect):
        # 设置
        mock_cursor = MagicMock()
        mock_connect.return_value.cursor.return_value = mock_cursor
        mock_cursor.fetchall.return_value = [(
            'SELECT * FROM table WHERE t.insert_day BETWEEN \"{captureStartDay}\" AND \"{captureEndDay}\"',
            '{\"spark.driver.memory\":\"4g\",\"spark.driver.cores\":\"4\",\"spark.executor.memory\":\"8G\",\"spark.executor.instances\":\"8\",\"spark.executor.cores\":\"4\",\"spark.default.parallelism\":\"32\"}')]
        mysql_config = json.dumps(
            {"user": "u", "password": "password", "host": "ip", "database": "ads_ops", "raise_on_warnings": "true"})
        result = getProductSql(2, mysql_config)

        self.assertEqual(result, (
            'SELECT * FROM table WHERE t.insert_day BETWEEN \"{captureStartDay}\" AND \"{captureEndDay}\"',
            '{\"spark.driver.memory\":\"4g\",\"spark.driver.cores\":\"4\",\"spark.executor.memory\":\"8G\",\"spark.executor.instances\":\"8\",\"spark.executor.cores\":\"4\",\"spark.default.parallelism\":\"32\"}'))
        mock_cursor.close.assert_called_once()
        mock_connect.return_value.close.assert_called_once()

    @patch('start_radius_association_detail.loadToDorisForCompleteDataCSV')
    @patch('start_radius_association_detail.execSparkCommand')
    @patch('start_radius_association_detail.getProductSql')
    @patch('argparse.ArgumentParser.parse_args')
    def test_main_success_flow(self, mock_parse, mock_get_sql, mock_exec, mock_load):
        # 修正参数配置结构
        mock_parse.return_value = argparse.Namespace(
            task_conf=json.dumps({
                "mysql_conf": {"host": "test_host"},
                "spark_task_name": "test_task",
                "spark_remote_host": "remote_host",
                "doris_conf": {"host": "doris_host"},
                "radius_association": json.dumps({  # 添加JSON序列化
                    "mobilenet_start_hour": "00",
                    "mobilenet_end_hour": "23",
                    "fixed_start_hour": "00",
                    "fixed_end_hour": "23",
                    "limit_count": 1000,
                    "detail_limit_count": 1000,
                    "logout_delay_seconds": 30,
                    "online_limit_min": 2
                }),
                "target_table_name": "test_table",
                "columns": "col1,col2"
            }),
            sql_id="87",
            capture_day="2025-03-19",
            start_time="1742313600000",  # 补全必须参数
            end_time="1742317200000"     # 补全必须参数
        )

        # 模拟完整SQL响应
        mock_get_sql.return_value = ("SELECT * FROM table", '{"spark.driver.memory":"4g"}')
        mock_exec.return_value = "dummy_output"

        main()

        # 验证参数传递正确性
        args, _ = mock_exec.call_args
        self.assertEqual(args[3], "2025-03-19")  # captureDay参数验证
        
if __name__ == '__main__':
    unittest.main()
