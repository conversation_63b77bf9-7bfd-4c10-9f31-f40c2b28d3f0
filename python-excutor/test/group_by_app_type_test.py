import unittest
from unittest.mock import patch, MagicMock

import sys
import os

# 添加父目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from common_util.es_common_util import calculate_string_md5
from start_spark_sql_arc import split_lines as mock_split_lines


# 导入要测试的函数
def group_by_app_type(archive_datas):
    """
    根据archive_data中的app_type进行分组
    :param archive_datas: archive_data的集合
    :return: 按app_type分组后的结果
    """
    # 创建一个字典来存储分组结果
    grouped_data = {}
    if archive_datas is None or len(archive_datas) == 0:
        return grouped_data
    # 遍历所有archive_data
    for archive_data in archive_datas:
        # 获取app_type，如果不存在则设为"未知"
        app_type = archive_data.get("app_type", "未知")

        # 如果该app_type还没有在结果字典中，则创建一个空列表
        if app_type not in grouped_data:
            grouped_data[app_type] = []
        # 将当前archive_data添加到对应app_type的列表中
        grouped_data[app_type].append(archive_data)
    return grouped_data


class TestGroupByAppType(unittest.TestCase):
    """测试按app_type分组功能的测试套件"""

    def test_empty_input(self):
        """测试空输入列表的情况"""
        result = group_by_app_type([])
        self.assertEqual(result, {})
        self.assertIsInstance(result, dict)

    def test_single_app_type(self):
        """测试只有一种app_type的情况"""
        archive_datas = [
            {"archive_name": "账号1", "app_type": "微信"},
            {"archive_name": "账号2", "app_type": "微信"},
            {"archive_name": "账号3", "app_type": "微信"}
        ]

        result = group_by_app_type(archive_datas)

        self.assertEqual(len(result), 1)
        self.assertIn("微信", result)
        self.assertEqual(len(result["微信"]), 3)
        self.assertEqual(result["微信"], archive_datas)

    def test_multiple_app_types(self):
        """测试多种app_type的情况"""
        archive_datas = [
            {"archive_name": "账号1", "app_type": "微信"},
            {"archive_name": "账号2", "app_type": "QQ"},
            {"archive_name": "账号3", "app_type": "微信"},
            {"archive_name": "账号4", "app_type": "钉钉"}
        ]

        result = group_by_app_type(archive_datas)

        self.assertEqual(len(result), 3)
        self.assertIn("微信", result)
        self.assertIn("QQ", result)
        self.assertIn("钉钉", result)

        self.assertEqual(len(result["微信"]), 2)
        self.assertEqual(len(result["QQ"]), 1)
        self.assertEqual(len(result["钉钉"]), 1)

        self.assertEqual(result["微信"][0]["archive_name"], "账号1")
        self.assertEqual(result["微信"][1]["archive_name"], "账号3")
        self.assertEqual(result["QQ"][0]["archive_name"], "账号2")
        self.assertEqual(result["钉钉"][0]["archive_name"], "账号4")

    def test_missing_app_type(self):
        """测试缺少app_type字段的情况"""
        archive_datas = [
            {"archive_name": "账号1", "app_type": "微信"},
            {"archive_name": "账号2"},  # 缺少app_type
            {"archive_name": "账号3", "app_type": "微信"},
            {"archive_name": "账号4", "app_type": ""}  # 空app_type
        ]

        result = group_by_app_type(archive_datas)

        self.assertEqual(len(result), 3)
        self.assertIn("微信", result)
        self.assertIn("未知", result)
        self.assertIn("", result)

        self.assertEqual(len(result["微信"]), 2)
        self.assertEqual(len(result["未知"]), 1)
        self.assertEqual(len(result[""]), 1)

        self.assertEqual(result["未知"][0]["archive_name"], "账号2")
        self.assertEqual(result[""][0]["archive_name"], "账号4")

    def test_preserve_original_data(self):
        """测试分组后原始数据是否完整保留"""
        archive_datas = [
            {"archive_name": "账号1", "app_type": "微信", "extra_field": "额外数据1"},
            {"archive_name": "账号2", "app_type": "QQ", "extra_field": "额外数据2"}
        ]

        result = group_by_app_type(archive_datas)

        self.assertEqual(result["微信"][0], {"archive_name": "账号1", "app_type": "微信", "extra_field": "额外数据1"})
        self.assertEqual(result["QQ"][0], {"archive_name": "账号2", "app_type": "QQ", "extra_field": "额外数据2"})

    def test_integration_with_split_lines(self):
        """测试与split_lines函数集成的情况"""
        # 模拟split_lines函数的输出
        mock_output = """账号1\t微信
账号2\tQQ
账号3\t微信
账号4\t钉钉"""

        # 使用模拟的split_lines处理输出
        archive_datas = mock_split_lines(mock_output, "deye_v64_quanxidangan_phone")

        # 测试分组结果
        result = group_by_app_type(archive_datas)

        self.assertEqual(len(result), 3)
        self.assertEqual(len(result["微信"]), 2)
        self.assertEqual(len(result["QQ"]), 1)
        self.assertEqual(len(result["钉钉"]), 1)

    def test_none_with_split_lines(self):
        """测试与split_lines函数集成的情况"""
        # 模拟split_lines函数的输出
        mock_output = """"""

        # 使用模拟的split_lines处理输出
        archive_datas = mock_split_lines(mock_output,"deye_v64_quanxidangan_phone")

        # 测试分组结果
        result = group_by_app_type(archive_datas)

        self.assertEqual(0, len(result))

    def test_calculate_string_md5(self):
        app1 = "autocad_oa"
        app1_res = "c12095680f8d0d5b7dac81c02e4fd097"
        app2 = "1.1.1.1_proxytool"
        app2_res = "2bff3e6c89d6342301d1345cfff81ba3"
        im1 = "103_101565711030001"
        im1_res = "82c682800bd519b3ee3c5da074752b4e"
        im2 = "103_10305805771030001"
        im2_res = "23d21fa8753fad5deb08a326df9b5896"

        radius = "1020001_038486217"
        radius_res = "c7c05f320f8d6ed7deb60c537d567a39"

        self.assertEqual(app1_res, calculate_string_md5(app1))
        self.assertEqual(app2_res, calculate_string_md5(app2))
        self.assertEqual(im1_res, calculate_string_md5(im1))
        self.assertEqual(im2_res, calculate_string_md5(im2))
        self.assertEqual(radius_res, calculate_string_md5(radius))


if __name__ == '__main__':
    unittest.main()
